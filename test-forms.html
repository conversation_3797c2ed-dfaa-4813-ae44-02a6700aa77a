<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .type-selector {
            margin-bottom: 20px;
        }
        select {
            padding: 8px;
            font-size: 16px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .form-preview {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .field-info {
            margin: 5px 0;
            padding: 5px;
            background: white;
            border-radius: 3px;
        }
        .required {
            color: #e74c3c;
        }
        .optional {
            color: #3498db;
        }
    </style>
</head>
<body>
    <h1>表单配置测试页面</h1>
    
    <div class="test-container">
        <h3>分析类型选择</h3>
        <div class="type-selector">
            <label for="analysis-type">选择分析类型：</label>
            <select id="analysis-type" onchange="updateFormPreview()">
                <option value="mingpan">命盘解析</option>
                <option value="daily">每日运势</option>
                <option value="marriage">合婚分析</option>
                <option value="career">事业合作</option>
                <option value="family">婆媳关系</option>
                <option value="friendship">知己分析</option>
                <option value="decision">每日决策</option>
                <option value="business">事业决策</option>
                <option value="investment">投资理财</option>
                <option value="tarot">塔罗解读</option>
                <option value="love_tarot">爱情塔罗</option>
                <option value="career_tarot">事业塔罗</option>
                <option value="calendar">黄历查询</option>
                <option value="date_selection">择日分析</option>
            </select>
        </div>
        
        <div class="form-preview" id="form-preview">
            <!-- 表单预览将在这里显示 -->
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="testAnalysisPage()">在分析页面测试</button>
        </div>
    </div>

    <script>
        function updateFormPreview() {
            const analysisType = document.getElementById('analysis-type').value;
            const preview = document.getElementById('form-preview');
            
            let fields = [];
            
            // 基础字段（所有类型都有）
            fields.push('<div class="field-info required">✓ 姓名 (必填)</div>');
            fields.push('<div class="field-info required">✓ 出生日期 (必填)</div>');
            fields.push('<div class="field-info required">✓ 出生时间 (必填)</div>');
            fields.push('<div class="field-info required">✓ 性别 (必填)</div>');
            fields.push('<div class="field-info required">✓ 出生地点 (必填)</div>');
            
            // 根据分析类型添加额外字段
            switch(analysisType) {
                case 'marriage':
                case 'career':
                case 'family':
                case 'friendship':
                    fields.push('<div class="field-info required">✓ 对方姓名 (必填)</div>');
                    fields.push('<div class="field-info required">✓ 对方出生日期 (必填)</div>');
                    fields.push('<div class="field-info required">✓ 对方出生时间 (必填)</div>');
                    fields.push('<div class="field-info required">✓ 对方性别 (必填)</div>');
                    break;
                    
                case 'decision':
                case 'business':
                case 'investment':
                case 'tarot':
                case 'love_tarot':
                case 'career_tarot':
                    fields.push('<div class="field-info required">✓ 具体问题 (必填)</div>');
                    break;
                    
                case 'calendar':
                case 'date_selection':
                    fields.push('<div class="field-info required">✓ 查询日期 (必填)</div>');
                    fields.push('<div class="field-info required">✓ 事件类型 (必填)</div>');
                    break;
            }
            
            preview.innerHTML = `
                <h4>当前分析类型：${getAnalysisTitle(analysisType)}</h4>
                <p><strong>需要的表单字段：</strong></p>
                ${fields.join('')}
            `;
        }
        
        function getAnalysisTitle(type) {
            const titles = {
                mingpan: '命盘解析',
                daily: '每日运势',
                marriage: '合婚分析',
                career: '事业合作',
                family: '婆媳关系',
                friendship: '知己分析',
                decision: '每日决策',
                business: '事业决策',
                investment: '投资理财',
                tarot: '塔罗解读',
                love_tarot: '爱情塔罗',
                career_tarot: '事业塔罗',
                calendar: '黄历查询',
                date_selection: '择日分析'
            };
            return titles[type] || type;
        }
        
        function testAnalysisPage() {
            const analysisType = document.getElementById('analysis-type').value;
            window.open(`analysis.html?type=${analysisType}`, '_blank');
        }
        
        // 初始化
        updateFormPreview();
    </script>
</body>
</html>
