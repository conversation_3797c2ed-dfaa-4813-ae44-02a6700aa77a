<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录/注册 - TinyDest</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">TinyDest</div>
            <div class="nav-links">
                <a href="index.html">主页</a>
                <a href="login.html">登录/注册</a>
            </div>
        </nav>
    </header>

    <main>
        <section class="auth-container">
            <div class="auth-header">
                <h1 class="auth-title">TinyDest</h1>
            </div>

            <div class="auth-content">
                <!-- 登录表单 -->
                <div id="login-form" class="auth-form active">
                    <h2 id="form-title">登录</h2>

                    <div class="auth-card">
                        <div class="auth-tabs">
                            <button class="tab-btn active" onclick="showTab('login')">登录</button>
                            <button class="tab-btn" onclick="showTab('register')">注册</button>
                        </div>

                        <h3 id="card-title">用户名/密码登录</h3>

                        <form id="loginForm">
                            <div class="form-group">
                                <label for="login-username">用户名 *</label>
                                <input type="text" id="login-username" name="username" required>
                            </div>
                            <div class="form-group">
                                <label for="login-password">密码 *</label>
                                <input type="password" id="login-password" name="password" required>
                            </div>
                            <button type="submit" class="submit-btn">登录</button>
                        </form>

                        <div class="auth-switch">
                            <span>还没有账户？</span>
                            <button type="button" class="switch-btn" onclick="showTab('register')">点击注册</button>
                        </div>

                        <div class="other-methods">
                            <p>或选择其他登录方式:</p>
                            <div class="other-method-buttons">
                                <button type="button" class="other-method-btn disabled" disabled>手机验证码登录</button>
                                <button type="button" class="other-method-btn disabled" disabled>Google 登录</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 注册表单 -->
                <div id="register-form" class="auth-form">
                    <h2 id="form-title-register">注册</h2>

                    <div class="auth-card">
                        <div class="auth-tabs">
                            <button class="tab-btn" onclick="showTab('login')">登录</button>
                            <button class="tab-btn active" onclick="showTab('register')">注册</button>
                        </div>

                        <h3 id="card-title-register">用户名/密码注册</h3>

                        <form id="registerForm">
                            <div class="form-group">
                                <label for="register-username">用户名 *</label>
                                <input type="text" id="register-username" name="username" required>
                            </div>
                            <div class="form-group">
                                <label for="register-password">密码 *</label>
                                <input type="password" id="register-password" name="password" required>
                            </div>
                            <div class="form-group">
                                <label for="register-confirm-password">确认密码 *</label>
                                <input type="password" id="register-confirm-password" name="confirmPassword" required>
                            </div>
                            <button type="submit" class="submit-btn">注册</button>
                        </form>

                        <div class="auth-switch">
                            <span>已有账户？</span>
                            <button type="button" class="switch-btn" onclick="showTab('login')">点击登录</button>
                        </div>

                        <div class="other-methods">
                            <p>或选择其他登录方式:</p>
                            <div class="other-method-buttons">
                                <button type="button" class="other-method-btn disabled" disabled>手机验证码登录</button>
                                <button type="button" class="other-method-btn disabled" disabled>Google 登录</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="auth.js"></script>
</body>
</html>
