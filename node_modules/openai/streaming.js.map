{"version": 3, "file": "streaming.js", "sourceRoot": "", "sources": ["src/streaming.ts"], "names": [], "mappings": ";;;AAAA,6CAA+D;AAC/D,sCAAsC;AACtC,sDAA+E;AAC/E,6DAAwE;AAExE,oCAA+C;AAC/C,sCAAmC;AAUnC,MAAa,MAAM;IAGjB,YACU,QAAmC,EAC3C,UAA2B;QADnB,aAAQ,GAAR,QAAQ,CAA2B;QAG3C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED,MAAM,CAAC,eAAe,CAAO,QAAkB,EAAE,UAA2B;QAC1E,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,KAAK,SAAS,CAAC,CAAC,QAAQ;YACtB,IAAI,QAAQ,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;aAC7F;YACD,QAAQ,GAAG,IAAI,CAAC;YAChB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI;gBACF,IAAI,KAAK,EAAE,MAAM,GAAG,IAAI,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE;oBAC9D,IAAI,IAAI;wBAAE,SAAS;oBAEnB,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;wBACjC,IAAI,GAAG,IAAI,CAAC;wBACZ,SAAS;qBACV;oBAED,IACE,GAAG,CAAC,KAAK,KAAK,IAAI;wBAClB,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC;wBACjC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,EACnC;wBACA,IAAI,IAAI,CAAC;wBAET,IAAI;4BACF,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;yBAC7B;wBAAC,OAAO,CAAC,EAAE;4BACV,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;4BAC9D,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;4BACtC,MAAM,CAAC,CAAC;yBACT;wBAED,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;4BACtB,MAAM,IAAI,gBAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,EAAE,IAAA,4BAAqB,EAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;yBAC/F;wBAED,MAAM,IAAI,CAAC;qBACZ;yBAAM;wBACL,IAAI,IAAI,CAAC;wBACT,IAAI;4BACF,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;yBAC7B;wBAAC,OAAO,CAAC,EAAE;4BACV,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;4BAC9D,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;4BACtC,MAAM,CAAC,CAAC;yBACT;wBACD,kDAAkD;wBAClD,IAAI,GAAG,CAAC,KAAK,IAAI,OAAO,EAAE;4BACxB,MAAM,IAAI,gBAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;yBACpE;wBACD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAS,CAAC;qBAC/C;iBACF;gBACD,IAAI,GAAG,IAAI,CAAC;aACb;YAAC,OAAO,CAAC,EAAE;gBACV,kFAAkF;gBAClF,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,YAAY;oBAAE,OAAO;gBAC1D,MAAM,CAAC,CAAC;aACT;oBAAS;gBACR,mDAAmD;gBACnD,IAAI,CAAC,IAAI;oBAAE,UAAU,CAAC,KAAK,EAAE,CAAC;aAC/B;QACH,CAAC;QAED,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,kBAAkB,CAAO,cAA8B,EAAE,UAA2B;QACzF,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,KAAK,SAAS,CAAC,CAAC,SAAS;YACvB,MAAM,WAAW,GAAG,IAAI,kBAAW,EAAE,CAAC;YAEtC,MAAM,IAAI,GAAG,IAAA,4CAA6B,EAAQ,cAAc,CAAC,CAAC;YAClE,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,IAAI,EAAE;gBAC9B,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oBAC5C,MAAM,IAAI,CAAC;iBACZ;aACF;YAED,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,KAAK,EAAE,EAAE;gBACtC,MAAM,IAAI,CAAC;aACZ;QACH,CAAC;QAED,KAAK,SAAS,CAAC,CAAC,QAAQ;YACtB,IAAI,QAAQ,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;aAC7F;YACD,QAAQ,GAAG,IAAI,CAAC;YAChB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI;gBACF,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,SAAS,EAAE,EAAE;oBACpC,IAAI,IAAI;wBAAE,SAAS;oBACnB,IAAI,IAAI;wBAAE,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;iBAClC;gBACD,IAAI,GAAG,IAAI,CAAC;aACb;YAAC,OAAO,CAAC,EAAE;gBACV,kFAAkF;gBAClF,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,YAAY;oBAAE,OAAO;gBAC1D,MAAM,CAAC,CAAC;aACT;oBAAS;gBACR,mDAAmD;gBACnD,IAAI,CAAC,IAAI;oBAAE,UAAU,CAAC,KAAK,EAAE,CAAC;aAC/B;QACH,CAAC;QAED,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC1C,CAAC;IAED,CAAC,MAAM,CAAC,aAAa,CAAC;QACpB,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,GAAG;QACD,MAAM,IAAI,GAAyC,EAAE,CAAC;QACtD,MAAM,KAAK,GAAyC,EAAE,CAAC;QACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEjC,MAAM,WAAW,GAAG,CAAC,KAA2C,EAAuB,EAAE;YACvF,OAAO;gBACL,IAAI,EAAE,GAAG,EAAE;oBACT,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;wBACtB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;wBAC/B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAClB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBACpB;oBACD,OAAO,KAAK,CAAC,KAAK,EAAG,CAAC;gBACxB,CAAC;aACF,CAAC;QACJ,CAAC,CAAC;QAEF,OAAO;YACL,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC;YACpD,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC;SACtD,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,gBAAgB;QACd,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,IAAyB,CAAC;QAC9B,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAElC,OAAO,IAAI,sBAAc,CAAC;YACxB,KAAK,CAAC,KAAK;gBACT,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;YACtC,CAAC;YACD,KAAK,CAAC,IAAI,CAAC,IAAS;gBAClB,IAAI;oBACF,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;oBAC1C,IAAI,IAAI;wBAAE,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;oBAE9B,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;oBAE3D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;iBACrB;gBAAC,OAAO,GAAG,EAAE;oBACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;iBACjB;YACH,CAAC;YACD,KAAK,CAAC,MAAM;gBACV,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACxB,CAAC;SACF,CAAC,CAAC;IACL,CAAC;CACF;AA5LD,wBA4LC;AAEM,KAAK,SAAS,CAAC,CAAC,gBAAgB,CACrC,QAAkB,EAClB,UAA2B;IAE3B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;QAClB,UAAU,CAAC,KAAK,EAAE,CAAC;QACnB,MAAM,IAAI,mBAAW,CAAC,mDAAmD,CAAC,CAAC;KAC5E;IAED,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IACpC,MAAM,WAAW,GAAG,IAAI,kBAAW,EAAE,CAAC;IAEtC,MAAM,IAAI,GAAG,IAAA,4CAA6B,EAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjE,IAAI,KAAK,EAAE,MAAM,QAAQ,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;QAChD,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YAC/C,MAAM,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACpC,IAAI,GAAG;gBAAE,MAAM,GAAG,CAAC;SACpB;KACF;IAED,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,KAAK,EAAE,EAAE;QACtC,MAAM,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,GAAG;YAAE,MAAM,GAAG,CAAC;KACpB;AACH,CAAC;AAxBD,4CAwBC;AAED;;;GAGG;AACH,KAAK,SAAS,CAAC,CAAC,aAAa,CAAC,QAAsC;IAClE,IAAI,IAAI,GAAG,IAAI,UAAU,EAAE,CAAC;IAE5B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,QAAQ,EAAE;QAClC,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,SAAS;SACV;QAED,MAAM,WAAW,GACf,KAAK,YAAY,WAAW,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC;YACpD,CAAC,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,CAAC,CAAC,KAAK,CAAC;QAEV,IAAI,OAAO,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClB,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,GAAG,OAAO,CAAC;QAEf,IAAI,YAAY,CAAC;QACjB,OAAO,CAAC,YAAY,GAAG,IAAA,6BAAsB,EAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YAC3D,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;YAClC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;SACjC;KACF;IAED,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QACnB,MAAM,IAAI,CAAC;KACZ;AACH,CAAC;AAED,MAAM,UAAU;IAKd;QACE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IACnB,CAAC;IAED,MAAM,CAAC,IAAY;QACjB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACvB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,IAAI,EAAE;YACT,6DAA6D;YAC7D,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;gBAAE,OAAO,IAAI,CAAC;YAElD,MAAM,GAAG,GAAoB;gBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC1B,GAAG,EAAE,IAAI,CAAC,MAAM;aACjB,CAAC;YAEF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;YAEjB,OAAO,GAAG,CAAC;SACZ;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEvB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACxB,OAAO,IAAI,CAAC;SACb;QAED,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAEjD,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACzB,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SAC5B;QAED,IAAI,SAAS,KAAK,OAAO,EAAE;YACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACpB;aAAM,IAAI,SAAS,KAAK,MAAM,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACvB;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED,SAAS,SAAS,CAAC,GAAW,EAAE,SAAiB;IAC/C,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACrC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;KACtF;IAED,OAAO,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACvB,CAAC"}