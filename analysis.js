// 分析页面的JavaScript逻辑

// 模块关系选项配置
const moduleRelationships = {
  family: ["婆婆", "媳妇"],
  father_son: ["父亲", "儿子"],
  mother_son: ["母亲", "儿子"],
  leadership: ["领导", "下属"],
}

// 模块对方标签配置
const modulePersonLabels = {
  marriage: "配偶",
  career: "合作伙伴",
  family: "对方",
  friendship: "朋友",
  leadership: "对方",
  father_son: "对方",
  mother_son: "对方",
}

// 获取URL参数中的分析类型
function getAnalysisType() {
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get("type") || "mingpan"
}

// 设置页面标题和表单标题
function setPageTitle() {
  const analysisType = getAnalysisType()
  const titles = {
    mingpan: "命盘解析",
    daily: "每日运势",
    marriage: "合婚分析",
    career: "事业合作",
    family: "婆媳关系",
    friendship: "知己分析",
    leadership: "领导下属",
    father_son: "父子关系",
    mother_son: "母子关系",
    decision: "每日决策",
    business: "事业决策",
    investment: "投资理财",
    calendar: "黄历查询",
    date_selection: "择日分析",
  }

  const title = titles[analysisType] || "命盘解析"
  document.title = `${title} - TinyDest`
  document.getElementById("analysis-title").textContent = title
}

// 根据分析类型配置表单字段
function configureFormFields() {
  const analysisType = getAnalysisType()

  // 隐藏所有可选字段
  hideAllOptionalFields()

  // 配置自己信息的必填项
  configureSelfRequiredFields()

  // 根据分析类型显示相应字段
  switch (analysisType) {
    case "marriage":
    case "career":
    case "family":
    case "friendship":
    case "leadership":
    case "father_son":
    case "mother_son":
      // 需要对方信息的分析
      showPartnerFields()
      break

    case "decision":
    case "business":
    case "investment":
      // 需要具体问题的分析
      showQuestionField()
      break

    case "calendar":
    case "date_selection":
      // 需要日期查询的分析
      showDateQueryFields()
      break

    case "daily":
      // 每日运势不需要额外字段，只需要基础信息
      break

    default:
      // 默认只显示基础字段
      break
  }
}

// 配置自己信息的必填属性
function configureSelfRequiredFields() {
  // 自己信息的必填字段
  const selfRequiredFields = ["name", "birthdate", "gender", "location"]

  selfRequiredFields.forEach((fieldId) => {
    const input = document.getElementById(fieldId)
    if (input) {
      input.setAttribute("required", "required")
    }
  })

  // 出生时间为选填
  const birthtimeInput = document.getElementById("birthtime")
  if (birthtimeInput) {
    birthtimeInput.removeAttribute("required")
  }
}

// 隐藏所有可选字段
function hideAllOptionalFields() {
  // 隐藏整个对方信息部分
  const partnerInfo = document.getElementById("partner-info")
  if (partnerInfo) {
    partnerInfo.style.display = "none"
  }

  // 隐藏其他可选字段
  const optionalFields = [
    "question-group",
    "date-query-group",
    "event-type-group",
  ]

  optionalFields.forEach((fieldId) => {
    const field = document.getElementById(fieldId)
    if (field) {
      field.style.display = "none"
      // 移除必填属性
      const input = field.querySelector("input, select, textarea")
      if (input) {
        input.removeAttribute("required")
      }
    }
  })

  // 移除对方信息字段的必填属性
  const partnerFields = [
    "partner-relationship",
    "partner-name",
    "partner-birthdate",
    "partner-birthtime",
    "partner-gender",
    "partner-location",
  ]

  partnerFields.forEach((fieldId) => {
    const input = document.getElementById(fieldId)
    if (input) {
      input.removeAttribute("required")
    }
  })
}

// 显示对方信息字段
function showPartnerFields() {
  const analysisType = getAnalysisType()

  // 显示整个对方信息部分
  const partnerInfo = document.getElementById("partner-info")
  if (partnerInfo) {
    partnerInfo.style.display = "block"
  }

  // 配置必填项
  configurePartnerRequiredFields(analysisType)

  // 配置关系选择器
  configureRelationshipSelector(analysisType)

  // 更新对方标签
  updatePartnerLabel(analysisType)
}

// 配置对方字段的必填属性
function configurePartnerRequiredFields(analysisType) {
  // 基础必填字段
  const alwaysRequired = [
    "partner-relationship",
    "partner-name",
    "partner-gender",
  ]

  // 根据分析类型决定出生日期是否必填
  // 合婚分析和事业合作中出生日期为选填，其他为必填
  const birthdateOptional = ["marriage", "career"]
  const birthdateRequired = !birthdateOptional.includes(analysisType)

  // 设置必填属性
  alwaysRequired.forEach((fieldId) => {
    const input = document.getElementById(fieldId)
    if (input) {
      input.setAttribute("required", "required")
      // 更新标签显示星号
      const label = document.querySelector(`label[for="${fieldId}"]`)
      if (label && !label.textContent.includes("*")) {
        label.textContent += " *"
      }
    }
  })

  // 处理出生日期字段
  const birthdateInput = document.getElementById("partner-birthdate")
  const birthdateLabel = document.querySelector(
    'label[for="partner-birthdate"]'
  )

  if (birthdateInput && birthdateLabel) {
    if (birthdateRequired) {
      birthdateInput.setAttribute("required", "required")
      if (!birthdateLabel.textContent.includes("*")) {
        birthdateLabel.textContent = "出生日期 *"
      }
    } else {
      birthdateInput.removeAttribute("required")
      birthdateLabel.textContent = "出生日期"
    }
  }

  // 出生时间和出生地点始终为选填
  const optionalFields = ["partner-birthtime", "partner-location"]
  optionalFields.forEach((fieldId) => {
    const input = document.getElementById(fieldId)
    if (input) {
      input.removeAttribute("required")
    }
  })
}

// 配置关系选择器
function configureRelationshipSelector(analysisType) {
  const relationshipSelect = document.getElementById("partner-relationship")
  const relationshipGroup = document.getElementById(
    "partner-relationship-group"
  )

  if (!relationshipSelect || !relationshipGroup) return

  // 检查是否有预定义的关系选项
  if (moduleRelationships[analysisType]) {
    // 显示关系选择器
    relationshipGroup.style.display = "block"
    relationshipSelect.setAttribute("required", "required")

    // 清空现有选项
    relationshipSelect.innerHTML = '<option value="">请选择关系</option>'

    // 添加预定义的关系选项
    moduleRelationships[analysisType].forEach((relationship) => {
      const option = document.createElement("option")
      option.value = relationship
      option.textContent = relationship
      relationshipSelect.appendChild(option)
    })
  } else {
    // 隐藏关系选择器
    relationshipGroup.style.display = "none"
    relationshipSelect.removeAttribute("required")
  }
}

// 更新对方标签
function updatePartnerLabel(analysisType) {
  const partnerTitle = document.querySelector("#partner-info h3")
  if (partnerTitle && modulePersonLabels[analysisType]) {
    partnerTitle.textContent = modulePersonLabels[analysisType]
  }
}

// 显示问题字段
function showQuestionField() {
  const field = document.getElementById("question-group")
  if (field) {
    field.style.display = "block"
    const textarea = field.querySelector("textarea")
    if (textarea) {
      textarea.setAttribute("required", "required")
    }
  }
}

// 显示日期查询字段
function showDateQueryFields() {
  const fields = ["date-query-group", "event-type-group"]
  fields.forEach((fieldId) => {
    const field = document.getElementById(fieldId)
    if (field) {
      field.style.display = "block"
      const input = field.querySelector("input, select")
      if (input) {
        input.setAttribute("required", "required")
      }
    }
  })
}

// 检查表单完整性并更新按钮状态
function updateSubmitButtonState() {
  const form = document.getElementById("fortune-form")
  const submitBtn = form.querySelector(".submit-btn")
  const requiredFields = form.querySelectorAll(
    "input[required], select[required], textarea[required]"
  )

  let allFilled = true

  requiredFields.forEach((field) => {
    if (!field.value.trim()) {
      allFilled = false
    }
  })

  if (allFilled) {
    submitBtn.disabled = false
    submitBtn.style.background = "#000000"
  } else {
    submitBtn.disabled = true
    submitBtn.style.background = "#cccccc"
  }
}

// 表单提交处理
document.addEventListener("DOMContentLoaded", function () {
  // 设置页面标题
  setPageTitle()

  // 配置表单字段
  configureFormFields()

  // 检查登录状态并预填充用户信息
  checkLoginAndPrefill()

  const form = document.getElementById("fortune-form")
  const resultSection = document.getElementById("result-section")
  const loading = document.getElementById("loading")
  const resultContent = document.getElementById("result-content")

  // 初始化按钮状态（在字段配置后）
  setTimeout(() => {
    updateSubmitButtonState()
  }, 100)

  // 监听表单输入变化
  form.addEventListener("input", updateSubmitButtonState)
  form.addEventListener("change", updateSubmitButtonState)

  form.addEventListener("submit", async function (e) {
    e.preventDefault()

    // 获取表单数据
    const formData = new FormData(form)
    const data = {
      name: formData.get("name"),
      birthdate: formData.get("birthdate"),
      birthtime: formData.get("birthtime"),
      gender: formData.get("gender"),
      location: formData.get("location"),
      analysisType: getAnalysisType(),
      // 对方信息
      partner_relationship: formData.get("partner_relationship"),
      partner_name: formData.get("partner_name"),
      partner_birthdate: formData.get("partner_birthdate"),
      partner_birthtime: formData.get("partner_birthtime"),
      partner_gender: formData.get("partner_gender"),
      partner_location: formData.get("partner_location"),
      // 特殊字段
      question: formData.get("question"),
      date_query: formData.get("date_query"),
      event_type: formData.get("event_type"),
    }

    // 验证表单数据
    if (!validateForm(data)) {
      return
    }

    // 显示结果区域和加载状态
    resultSection.style.display = "block"
    loading.style.display = "block"
    resultContent.style.display = "none"

    // 滚动到结果区域
    resultSection.scrollIntoView({ behavior: "smooth" })

    try {
      // 首先尝试普通流式响应
      let success = false

      try {
        const response = await fetch("/api/analyze", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 处理流式响应
        await handleStreamResponse(response)
        success = true
      } catch (streamError) {
        console.warn("普通流式响应失败，尝试SSE方式:", streamError)

        // 如果普通流式失败，尝试SSE方式
        try {
          await handleSSEResponse(data)
          success = true
        } catch (sseError) {
          console.error("SSE响应也失败:", sseError)
          throw sseError
        }
      }

      if (!success) {
        throw new Error("所有流式响应方式都失败")
      }
    } catch (error) {
      console.error("请求失败:", error)
      displayError("网络错误，请检查网络连接后重试")
    }
  })
})

// 处理流式响应
async function handleStreamResponse(response) {
  const loading = document.getElementById("loading")
  const resultContent = document.getElementById("result-content")

  // 隐藏加载动画，显示结果区域
  loading.style.display = "none"
  resultContent.style.display = "block"
  resultContent.innerHTML = '<div class="stream-content"></div>'

  const streamContainer = resultContent.querySelector(".stream-content")
  const reader = response.body.getReader()
  const decoder = new TextDecoder()

  try {
    let buffer = ""

    while (true) {
      const { done, value } = await reader.read()

      if (done) {
        break
      }

      // 解码数据块
      const chunk = decoder.decode(value, { stream: true })
      buffer += chunk

      // 安全渲染HTML内容
      renderSafeHTML(streamContainer, buffer)
    }

    // 标记流式响应完成，移除光标效果
    streamContainer.classList.add("complete")
    console.log("流式响应接收完成")
  } catch (error) {
    console.error("流式响应处理错误:", error)
    displayError("接收数据时出现错误，请重新尝试")
  } finally {
    reader.releaseLock()
  }
}

// 处理SSE响应 (服务器环境备用方案)
async function handleSSEResponse(data) {
  const loading = document.getElementById("loading")
  const resultContent = document.getElementById("result-content")

  // 隐藏加载动画，显示结果区域
  loading.style.display = "none"
  resultContent.style.display = "block"
  resultContent.innerHTML = '<div class="stream-content"></div>'

  const streamContainer = resultContent.querySelector(".stream-content")
  let buffer = ""

  return new Promise((resolve, reject) => {
    // 创建EventSource连接
    const eventSource = new EventSource("/api/analyze-sse", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    })

    // 由于EventSource不支持POST，我们需要使用fetch来模拟SSE
    fetch("/api/analyze-sse", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const reader = response.body.getReader()
        const decoder = new TextDecoder()

        function readStream() {
          return reader.read().then(({ done, value }) => {
            if (done) {
              streamContainer.classList.add("complete")
              console.log("SSE流式响应接收完成")
              resolve()
              return
            }

            // 解码数据
            const chunk = decoder.decode(value, { stream: true })
            const lines = chunk.split("\n")

            for (const line of lines) {
              if (line.startsWith("data: ")) {
                try {
                  const data = JSON.parse(line.slice(6))

                  switch (data.type) {
                    case "connected":
                      console.log("SSE连接已建立")
                      break

                    case "content":
                      buffer += data.content
                      renderSafeHTML(streamContainer, buffer)
                      break

                    case "heartbeat":
                      console.log(`SSE心跳: chunk ${data.chunk}`)
                      break

                    case "complete":
                      console.log(`SSE完成: 总共 ${data.totalChunks} 个数据块`)
                      streamContainer.classList.add("complete")
                      resolve()
                      return

                    case "error":
                      console.error("SSE错误:", data.message)
                      reject(new Error(data.message))
                      return
                  }
                } catch (parseError) {
                  console.warn("解析SSE数据失败:", parseError, line)
                }
              }
            }

            return readStream()
          })
        }

        return readStream()
      })
      .catch((error) => {
        console.error("SSE请求失败:", error)
        reject(error)
      })
  })
}

// 安全渲染HTML内容
function renderSafeHTML(container, htmlContent) {
  // 创建一个临时容器来解析HTML
  const tempDiv = document.createElement("div")
  tempDiv.innerHTML = htmlContent

  // 清理和验证HTML内容
  const cleanHTML = sanitizeHTML(tempDiv.innerHTML)

  // 更新容器内容
  container.innerHTML = cleanHTML
}

// HTML内容清理和验证
function sanitizeHTML(html) {
  // 允许的HTML标签
  const allowedTags = ["div", "h3", "p", "strong", "ul", "li", "span", "br"]
  const allowedClasses = ["section-title", "fortune-analysis", "error"]

  // 创建临时DOM元素进行清理
  const tempDiv = document.createElement("div")
  tempDiv.innerHTML = html

  // 递归清理所有元素
  cleanElement(tempDiv, allowedTags, allowedClasses)

  return tempDiv.innerHTML
}

// 清理DOM元素
function cleanElement(element, allowedTags, allowedClasses) {
  const children = Array.from(element.children)

  children.forEach((child) => {
    // 检查标签是否被允许
    if (!allowedTags.includes(child.tagName.toLowerCase())) {
      // 如果标签不被允许，保留文本内容但移除标签
      const textContent = child.textContent
      const textNode = document.createTextNode(textContent)
      child.parentNode.replaceChild(textNode, child)
      return
    }

    // 清理class属性
    if (child.className) {
      const classes = child.className
        .split(" ")
        .filter((cls) => allowedClasses.includes(cls))
      child.className = classes.join(" ")
    }

    // 移除所有其他属性（除了class）
    const attributes = Array.from(child.attributes)
    attributes.forEach((attr) => {
      if (attr.name !== "class") {
        child.removeAttribute(attr.name)
      }
    })

    // 递归处理子元素
    cleanElement(child, allowedTags, allowedClasses)
  })
}

// 验证表单数据
function validateForm(data) {
  if (!data.name.trim()) {
    alert("请输入姓名")
    return false
  }

  if (!data.birthdate) {
    alert("请选择出生日期")
    return false
  }

  if (!data.birthtime) {
    alert("请选择出生时间")
    return false
  }

  if (!data.gender) {
    alert("请选择性别")
    return false
  }

  if (!data.location.trim()) {
    alert("请输入出生地点")
    return false
  }

  return true
}

// 显示分析结果 (已废弃，使用流式渲染)
// function displayResult(analysis) {
//   const loading = document.getElementById("loading")
//   const resultContent = document.getElementById("result-content")

//   loading.style.display = "none"
//   resultContent.style.display = "block"

//   // 将分析结果转换为HTML格式
//   const formattedAnalysis = formatAnalysis(analysis)
//   resultContent.innerHTML = formattedAnalysis
// }

// 显示错误信息
function displayError(errorMessage) {
  const loading = document.getElementById("loading")
  const resultContent = document.getElementById("result-content")

  loading.style.display = "none"
  resultContent.style.display = "block"
  resultContent.innerHTML = `
        <div class="error-message">
            <h3>😔 分析失败</h3>
            <p>${errorMessage}</p>
            <button onclick="location.reload()" class="retry-btn">重新尝试</button>
        </div>
    `
}

// 格式化分析结果
function formatAnalysis(analysis) {
  // 将换行符转换为HTML换行
  let formatted = analysis.replace(/\n/g, "<br>")

  // 处理特殊格式
  formatted = formatted.replace(/【([^】]+)】/g, "<h3>$1</h3>")
  formatted = formatted.replace(/🌟/g, '<span class="star">🌟</span>')
  formatted = formatted.replace(/✨/g, '<span class="sparkle">✨</span>')

  // 处理列表项
  formatted = formatted.replace(/(\d+\.\s)/g, "<strong>$1</strong>")
  formatted = formatted.replace(/(-\s)/g, '<span class="bullet">•</span> ')

  return `<div class="analysis-result">${formatted}</div>`
}

// 返回主页
function goHome() {
  window.location.href = "index.html"
}

// 检查登录状态并预填充用户信息
async function checkLoginAndPrefill() {
  try {
    const response = await fetch("/api/user")
    const result = await response.json()

    if (result.success && result.user) {
      // 已登录，更新导航链接
      const authLink = document.getElementById("auth-link")
      if (authLink) {
        authLink.innerHTML = `
          <span>${result.user.username}</span>
          <div class="dropdown">
            <a href="profile.html">个人中心</a>
            <a href="#" onclick="logout()">退出登录</a>
          </div>
        `
        authLink.href = "#"
        authLink.id = "user-menu"
      }

      // 获取用户个人信息并预填充
      await prefillUserInfo()
    }
  } catch (error) {
    console.log("未登录或检查登录状态失败")
  }
}

// 预填充用户信息
async function prefillUserInfo() {
  try {
    const response = await fetch("/api/profile")
    const result = await response.json()

    if (result.success && result.profile) {
      const profile = result.profile
      const form = document.getElementById("fortune-form")

      // 预填充基本信息
      const fieldMapping = {
        name: "name",
        birthdate: "birthdate",
        birthtime: "birthtime",
        gender: "gender",
        location: "location",
      }

      Object.keys(fieldMapping).forEach((formField) => {
        const profileField = fieldMapping[formField]
        const input = form.querySelector(`[name="${formField}"]`)
        if (input && profile[profileField]) {
          input.value = profile[profileField]
        }
      })

      // 更新按钮状态
      setTimeout(() => {
        updateSubmitButtonState()
      }, 200)
    }
  } catch (error) {
    console.error("预填充用户信息失败:", error)
  }
}

// 退出登录
async function logout() {
  try {
    const response = await fetch("/api/logout", {
      method: "POST",
    })

    const result = await response.json()

    if (result.success) {
      alert("已退出登录")
      window.location.href = "index.html"
    }
  } catch (error) {
    console.error("退出登录失败:", error)
    alert("退出登录失败")
  }
}
