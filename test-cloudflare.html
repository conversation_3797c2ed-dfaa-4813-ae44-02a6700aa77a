<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloudflare 流式响应测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
            border: 1px solid #e9ecef;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .headers {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🌩️ Cloudflare 流式响应测试</h1>
    
    <div class="test-container">
        <h3>1. 简单流式测试</h3>
        <p>测试基本的流式响应功能（每500ms发送一个数据块）</p>
        <button onclick="testSimpleStream()" id="simpleBtn">开始简单测试</button>
        <div id="simpleResult" class="result"></div>
        <div id="simpleStatus"></div>
    </div>

    <div class="test-container">
        <h3>2. AI 分析流式测试</h3>
        <p>测试实际的 AI 分析流式响应</p>
        <button onclick="testAIStream()" id="aiBtn">开始 AI 测试</button>
        <div id="aiResult" class="result"></div>
        <div id="aiStatus"></div>
    </div>

    <div class="test-container">
        <h3>3. 响应头检查</h3>
        <p>检查服务器返回的响应头是否正确</p>
        <button onclick="checkHeaders()" id="headerBtn">检查响应头</button>
        <div id="headerResult" class="headers"></div>
    </div>

    <div class="test-container">
        <h3>4. 网络环境信息</h3>
        <div id="networkInfo" class="headers"></div>
    </div>

    <script>
        // 显示网络环境信息
        function showNetworkInfo() {
            const info = {
                'User Agent': navigator.userAgent,
                'URL': window.location.href,
                'Protocol': window.location.protocol,
                'Host': window.location.host,
                'Connection': navigator.connection ? navigator.connection.effectiveType : 'Unknown',
                'Timestamp': new Date().toISOString()
            };
            
            document.getElementById('networkInfo').textContent = 
                Object.entries(info).map(([key, value]) => `${key}: ${value}`).join('\n');
        }

        // 测试简单流式响应
        async function testSimpleStream() {
            const btn = document.getElementById('simpleBtn');
            const result = document.getElementById('simpleResult');
            const status = document.getElementById('simpleStatus');
            
            btn.disabled = true;
            result.textContent = '';
            status.innerHTML = '<div class="status info">正在测试简单流式响应...</div>';
            
            try {
                const response = await fetch('/api/test-stream');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let receivedData = '';
                let chunkCount = 0;
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) break;
                    
                    const chunk = decoder.decode(value, { stream: true });
                    receivedData += chunk;
                    chunkCount++;
                    
                    result.textContent = receivedData;
                    result.scrollTop = result.scrollHeight;
                    
                    console.log(`接收到数据块 ${chunkCount}:`, chunk);
                }
                
                status.innerHTML = `<div class="status success">✅ 简单流式测试成功！接收到 ${chunkCount} 个数据块</div>`;
                
            } catch (error) {
                console.error('简单流式测试失败:', error);
                status.innerHTML = `<div class="status error">❌ 简单流式测试失败: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
            }
        }

        // 测试 AI 分析流式响应
        async function testAIStream() {
            const btn = document.getElementById('aiBtn');
            const result = document.getElementById('aiResult');
            const status = document.getElementById('aiStatus');
            
            btn.disabled = true;
            result.textContent = '';
            status.innerHTML = '<div class="status info">正在测试 AI 分析流式响应...</div>';
            
            const testData = {
                name: "测试用户",
                birthdate: "1990-01-01",
                birthtime: "12:00",
                gender: "male",
                location: "北京",
                analysisType: "mingpan"
            };
            
            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let receivedData = '';
                let chunkCount = 0;
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) break;
                    
                    const chunk = decoder.decode(value, { stream: true });
                    receivedData += chunk;
                    chunkCount++;
                    
                    result.innerHTML = receivedData;
                    result.scrollTop = result.scrollHeight;
                    
                    console.log(`AI 数据块 ${chunkCount}:`, chunk);
                }
                
                status.innerHTML = `<div class="status success">✅ AI 流式测试成功！接收到 ${chunkCount} 个数据块</div>`;
                
            } catch (error) {
                console.error('AI 流式测试失败:', error);
                status.innerHTML = `<div class="status error">❌ AI 流式测试失败: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
            }
        }

        // 检查响应头
        async function checkHeaders() {
            const btn = document.getElementById('headerBtn');
            const result = document.getElementById('headerResult');
            
            btn.disabled = true;
            result.textContent = '正在检查响应头...';
            
            try {
                const response = await fetch('/api/test-stream');
                
                const headers = {};
                for (const [key, value] of response.headers.entries()) {
                    headers[key] = value;
                }
                
                const headerText = Object.entries(headers)
                    .map(([key, value]) => `${key}: ${value}`)
                    .join('\n');
                
                result.textContent = `响应状态: ${response.status} ${response.statusText}\n\n响应头:\n${headerText}`;
                
                // 检查关键响应头
                const criticalHeaders = [
                    'cache-control',
                    'content-encoding', 
                    'cf-cache-status',
                    'x-accel-buffering',
                    'transfer-encoding'
                ];
                
                const missing = criticalHeaders.filter(header => !headers[header]);
                if (missing.length > 0) {
                    result.textContent += `\n\n⚠️ 缺少关键响应头: ${missing.join(', ')}`;
                }
                
            } catch (error) {
                result.textContent = `检查响应头失败: ${error.message}`;
            } finally {
                btn.disabled = false;
            }
        }

        // 页面加载时显示网络信息
        showNetworkInfo();
    </script>
</body>
</html>
