# TinyDest - AI智能命理分析系统

## 项目概述
一个基于AI的算命网站，提供多种命理分析服务
网站名称：TinyDest
网站副标题：all the power on earth can't change destiny

## 核心功能

### 八字分析
- 命盘解析：揭示个人命盘特质与发展规律
- 每日运势：基于八字的每日运势分析
- 合婚分析：基于八字的深度匹配分析
- 事业合作：基于八字的商业关系分析
- 婆媳关系：婆媳关系分析和改善建议
- 知己分析：基于八字的朋友关系分析
- 领导下属：基于八字分析领导与下属的关系模式和管理建议
- 父子关系：基于八字分析父子关系的互动模式和改善方法
- 母子关系：基于八字分析母子关系的互动模式和改善方法

### 梅花易数
- 每日决策：基于梅花易数的每日决策指导
- 事业决策：运用梅花易数分析事业发展方向
- 投资理财：梅花易数指导投资时机和理财方向



### 黄历查询
- 黄历查询：传统中国黄历，查询各类活动的吉日
- 择日分析：根据个人八字选择最适合的吉日良辰

## 技术架构
- 前端：HTML + CSS + JavaScript
- 后端：Node.js + Express
- AI集成：OpenAI 库调用 LLM API 进行命理分析
- 配置管理：dotenv 加载环境变量
- 数据存储：better-sqlite3 数据库存储用户信息和分析结果
- 用户认证：bcryptjs 密码加密 + express-session 会话管理

## 环境配置
项目使用 .env 文件管理 API 配置：
- OPENAI_MODEL：使用的模型名称（如 deepseek-chat）
- OPENAI_API_KEY：API 密钥
- OPENAI_BASE_URL：API 基础 URL（如 https://api.deepseek.com/v1）

## 依赖包
- express：Web 框架
- openai：OpenAI 官方 JavaScript 库
- dotenv：环境变量管理
- better-sqlite3：SQLite 数据库
- bcryptjs：密码加密
- express-session：会话管理
- nodemon：开发时自动重启（开发依赖）

## 核心流程
1. 用户选择分析类型
2. 填写个人信息表单
3. 提交数据到后端 API (/api/analyze)
4. 后端构建分析提示词（要求返回HTML格式）
5. 使用 OpenAI 库进行流式 LLM API 调用
6. 实时流式传输分析结果到前端
7. 前端安全渲染HTML内容并实时显示

## 文件结构
- `index.html`: 主页，包含服务选择
- `analysis.html`: 分析页面，包含表单和结果显示
- `analysis.js`: 分析页面的前端逻辑
- `login.html`: 登录/注册页面
- `profile.html`: 个人中心页面
- `auth.js`: 认证页面的前端逻辑
- `profile.js`: 个人中心页面的前端逻辑
- `styles.css`: 全局样式文件
- `server.js`: Node.js 后端服务器
- `tinydest.db`: SQLite 数据库文件
- `.env`: 环境变量配置文件

## API 接口
### 用户认证接口
- **POST /api/register**: 用户注册
- **POST /api/login**: 用户登录
- **POST /api/logout**: 用户退出
- **GET /api/user**: 获取当前用户信息
- **GET /api/profile**: 获取用户资料
- **PUT /api/profile**: 更新用户资料

### 命理分析接口
- **POST /api/analyze**: 命理分析接口（流式响应）
  - 请求参数：name, birthdate, birthtime, gender, location, analysisType
  - 响应类型：text/plain 流式响应
  - 返回：实时流式HTML内容
  - 错误处理：API 调用失败时返回友好提示

## 技术特性
### 流式响应
- 使用 OpenAI 流式 API 实现实时响应
- 前端使用 Fetch API 的 ReadableStream 处理流式数据
- 实时渲染分析结果，提升用户体验
- **双重流式方案**：普通流式 + SSE 备用方案
- **服务器环境优化**：强化反缓存机制，解决部署问题

### 安全HTML渲染
- 服务端返回HTML格式内容而非Markdown
- 前端实现HTML内容清理和验证
- 只允许安全的HTML标签和CSS类
- 防止XSS攻击和恶意代码注入

### 部署优化
- 强化的HTTP响应头设置，禁用各级缓存
- Nginx/Apache 配置示例，确保流式响应正常工作
- 自动切换机制：普通流式失败时自动切换到SSE
- CDN 配置指导，避免API响应被缓存

## 前端功能
- 响应式设计，支持移动端
- 动态页面标题根据分析类型调整
- 智能表单字段显示（根据分析类型显示相应字段）
- 表单验证和错误提示
- 加载动画和结果格式化显示
- URL参数传递分析类型
- 错误处理和重试机制
- 黑白配色设计风格
- 必填项标注（*）
- 智能按钮状态管理（必填项未完成时按钮禁用）
- 返回主页链接

## 表单字段配置
### 基础字段（所有分析类型）
- 姓名、出生日期、出生时间、性别、出生地点

### 对方信息字段（双人分析）
- 合婚分析、事业合作、婆媳关系、知己分析、领导下属、父子关系、母子关系
- 包含：关系选择、对方姓名、出生日期、出生时间、性别、出生地点（选填）

### 问题字段（咨询类分析）
- 每日决策、事业决策、投资理财
- 包含：具体咨询问题

### 日期字段（黄历类分析）
- 黄历查询、择日分析
- 包含：查询日期、活动类型（结婚、开业、搬家、出行、投资、其他）

## 最新修改记录（TinyDest版本）

### 2025年修改内容
1. **网站品牌更新**
   - 网站名称：知命 → TinyDest
   - 副标题：精准解读，洞察未来 → all the power on earth can't change destiny
   - 移除了网站Logo

2. **功能模块调整**
   - **移除模块**：完全移除塔罗占卜相关的所有模块
   - **新增八字分析模块**：
     * 领导下属：基于八字分析领导与下属的关系模式和管理建议
     * 父子关系：基于八字分析父子关系的互动模式和改善方法
     * 母子关系：基于八字分析母子关系的互动模式和改善方法

3. **用户界面优化**
   - **配色方案**：改为黑白配色设计
   - **按钮文本**：统一改为"分析"
   - **必填项标注**：所有必填项后添加红色星号（*）
   - **返回链接**：每个分析页面添加"返回主页"链接

4. **表单功能增强**
   - **智能按钮状态**：必填项未完成时按钮显示灰色并禁用，完成后变为黑色可点击
   - **关系选择**：双人分析中新增关系下拉框（配偶、伴侣、同事、上司、下属、朋友、父亲、母亲、儿子、女儿、其他）
   - **出生地点**：对方信息中新增选填的出生地点字段
   - **问题输入优化**：梅花易数模块的问题输入框提示文字改为"请描述你想咨询的具体问题"
   - **活动类型**：黄历模块的"事件类型"改为"活动类型"

5. **技术实现**
   - **表单验证**：实时监听表单输入，动态更新按钮状态
   - **字段管理**：根据分析类型智能显示/隐藏相关字段
   - **数据处理**：后端支持新增的关系和地点字段
   - **样式优化**：CSS样式全面更新为黑白配色方案

6. **账户与个人中心系统**
   - **用户注册/登录**：
     * 支持用户名/密码注册登录（主要功能）
     * 手机验证码和Google登录显示为灰色禁用状态（预留功能）
     * 密码加密存储，会话管理
   - **个人中心功能**：
     * 标题为"我的信息"
     * 包含基本信息、联系信息、其他信息三个分组
     * 所有字段均为选填项
     * "保存修改"和"稍后填写"两个操作选项
   - **数据预填充**：
     * 用户登录后，分析表单自动预填充个人信息
     * 用户可随时修改预填充的信息
     * 实时更新表单验证状态
   - **数据库设计**：
     * users表：存储用户账户信息
     * user_profiles表：存储用户详细资料
     * 支持用户资料的增删改查操作

7. **表单结构优化**
   - **分组显示**：
     * "自己"黑体标题下显示个人信息
     * "对方"/"合作伙伴"/"朋友"等黑体标题下显示对方信息
     * 根据分析类型动态显示相应的对方标签
   - **关系限制**：
     * 婆媳关系：只能选择"婆婆"或"媳妇"
     * 父子关系：只能选择"父亲"或"儿子"
     * 母子关系：只能选择"母亲"或"儿子"
     * 领导下属：只能选择"领导"或"下属"
   - **智能表单配置**：
     * 单人分析只显示"自己"部分
     * 双人分析显示"自己"和"对方"两个部分
     * 关系选择器根据模块类型动态配置选项