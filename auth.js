// 认证页面的JavaScript逻辑

// 切换登录/注册标签
function showTab(tabName) {
  // 移除所有活动状态
  document
    .querySelectorAll(".tab-btn")
    .forEach((btn) => btn.classList.remove("active"))
  document
    .querySelectorAll(".auth-form")
    .forEach((form) => form.classList.remove("active"))

  // 激活选中的标签和表单
  event.target.classList.add("active")
  document.getElementById(tabName + "-form").classList.add("active")

  // 更新页面标题
  const formTitle = document.getElementById("form-title")
  const formTitleRegister = document.getElementById("form-title-register")
  const cardTitle = document.getElementById("card-title")
  const cardTitleRegister = document.getElementById("card-title-register")

  if (tabName === "login") {
    if (formTitle) formTitle.textContent = "登录"
    if (cardTitle) cardTitle.textContent = "用户名/密码登录"
  } else if (tabName === "register") {
    if (formTitleRegister) formTitleRegister.textContent = "注册"
    if (cardTitleRegister) cardTitleRegister.textContent = "用户名/密码注册"
  }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已登录
    checkLoginStatus()
    
    // 登录表单提交
    document.getElementById('loginForm').addEventListener('submit', async function(e) {
        e.preventDefault()
        
        const formData = new FormData(this)
        const data = {
            username: formData.get('username'),
            password: formData.get('password')
        }
        
        try {
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            
            const result = await response.json()
            
            if (result.success) {
                alert('登录成功！')
                // 跳转到个人中心或主页
                window.location.href = result.redirect || 'profile.html'
            } else {
                alert(result.message || '登录失败')
            }
        } catch (error) {
            console.error('登录错误:', error)
            alert('登录失败，请重试')
        }
    })
    
    // 注册表单提交
    document.getElementById('registerForm').addEventListener('submit', async function(e) {
        e.preventDefault()
        
        const formData = new FormData(this)
        const password = formData.get('password')
        const confirmPassword = formData.get('confirmPassword')
        
        // 验证密码
        if (password !== confirmPassword) {
            alert('两次输入的密码不一致')
            return
        }
        
        if (password.length < 6) {
            alert('密码长度至少6位')
            return
        }
        
        const data = {
            username: formData.get('username'),
            password: password
        }
        
        try {
            const response = await fetch('/api/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            
            const result = await response.json()
            
            if (result.success) {
                alert('注册成功！请登录')
                // 切换到登录标签
                showTab('login')
            } else {
                alert(result.message || '注册失败')
            }
        } catch (error) {
            console.error('注册错误:', error)
            alert('注册失败，请重试')
        }
    })
})

// 检查登录状态
async function checkLoginStatus() {
    try {
        const response = await fetch('/api/user')
        const result = await response.json()
        
        if (result.success && result.user) {
            // 已登录，跳转到个人中心
            window.location.href = 'profile.html'
        }
    } catch (error) {
        console.error('检查登录状态失败:', error)
    }
}

// 退出登录
async function logout() {
    try {
        const response = await fetch('/api/logout', {
            method: 'POST'
        })
        
        const result = await response.json()
        
        if (result.success) {
            alert('已退出登录')
            window.location.href = 'index.html'
        }
    } catch (error) {
        console.error('退出登录失败:', error)
        alert('退出登录失败')
    }
}
