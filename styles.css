* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

header {
    background: white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5%;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #000000;
}

.nav-links a {
    text-decoration: none;
    color: #666;
    margin-left: 2rem;
    transition: color 0.3s;
}

.nav-links a:hover {
    color: #000000;
}

/* 导航栏用户菜单特殊样式 */
.nav-links #user-menu {
    color: #666;
    margin-left: 2rem;
    transition: color 0.3s;
}

.nav-links #user-menu:hover {
    color: #000000;
}

.nav-links #user-menu > span {
    color: inherit;
    padding: 0;
}

.hero {
    text-align: center;
    padding: 4rem 2rem;
    background: #000000;
    color: white;
}

.hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.services {
    max-width: 1200px;
    margin: 0 auto;
    padding: 3rem 2rem;
}

.services h2 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    color: #000000;
}

.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s;
    border: 1px solid #e0e0e0;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-card h3 {
    margin-bottom: 1rem;
    color: #000000;
}

.service-card p {
    color: #666;
    margin-bottom: 1.5rem;
}

.service-card button {
    background: #000000;
    color: white;
    border: none;
    padding: 0.8rem 2rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s;
    width: 100%;
}

.service-card button:hover {
    background: #333333;
}

.analysis-form {
    max-width: 600px;
    margin: 2rem auto;
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.analysis-form h1 {
    text-align: center;
    margin-bottom: 2rem;
    color: #000000;
}

/* 分组标题样式 */
#self-info h3,
#partner-info h3 {
    font-size: 18px;
    font-weight: bold;
    color: #000000;
    margin: 30px 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #000000;
}

#self-info h3:first-child {
    margin-top: 0;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #555;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.submit-btn {
    width: 100%;
    background: #000000;
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background 0.3s;
}

.submit-btn:hover {
    background: #333333;
}

.submit-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
}

/* 返回主页链接样式 */
.back-link {
    display: inline-block;
    margin-bottom: 1rem;
    color: #666;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s;
}

.back-link:hover {
    color: #000000;
}

.back-link::before {
    content: "← ";
    margin-right: 0.3rem;
}

.result-section {
    max-width: 800px;
    margin: 2rem auto;
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.loading {
    text-align: center;
    padding: 2rem;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2c3e50;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.result-content {
    line-height: 1.8;
    color: #444;
}

.result-content h3 {
    color: #000000;
    margin: 1.5rem 0 1rem;
}

/* 分析结果样式 */
.analysis-result {
    font-size: 1.1rem;
    line-height: 1.8;
}

.analysis-result h3 {
    background: #000000;
    color: white;
    padding: 0.8rem 1.2rem;
    border-radius: 8px;
    margin: 1.5rem 0 1rem;
    font-size: 1.2rem;
}

.analysis-result .star,
.analysis-result .sparkle {
    font-size: 1.2em;
    margin: 0 0.2rem;
}

.analysis-result .bullet {
    color: #000000;
    font-weight: bold;
    margin-right: 0.5rem;
}

.analysis-result strong {
    color: #000000;
}

/* 错误信息样式 */
.error-message {
    text-align: center;
    padding: 2rem;
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    color: #c53030;
}

.error-message h3 {
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.retry-btn {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    margin-top: 1rem;
    transition: background 0.3s ease;
}

.retry-btn:hover {
    background: #c53030;
}

/* 流式内容样式 */
.stream-content {
    min-height: 100px;
    animation: fadeIn 0.3s ease-in;
}

.stream-content .section-title {
    background: #000000;
    color: white;
    padding: 0.8rem 1.2rem;
    border-radius: 8px;
    margin: 1.5rem 0 1rem;
    font-size: 1.2rem;
    display: block;
}

.stream-content p {
    margin: 1rem 0;
    line-height: 1.8;
}

.stream-content strong {
    color: #000000;
    font-weight: 600;
}

.stream-content ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.stream-content li {
    margin: 0.5rem 0;
    line-height: 1.6;
}

/* 打字机效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 流式加载指示器 */
.stream-content::after {
    content: '▋';
    animation: blink 1s infinite;
    color: #000000;
}

.stream-content.complete::after {
    display: none;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 表单字段样式 */
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
}

.form-group textarea:focus {
    outline: none;
    border-color: #000000;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2);
}

/* 对方信息区域样式 */
.partner-info {
    background: #f8f8f8;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 1rem 0;
    border-left: 4px solid #000000;
}

.partner-info h4 {
    color: #000000;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* 服务分类标题样式 */
.services h2 {
    color: #000000;
    border-bottom: 2px solid #000000;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
}

/* 服务卡片悬停效果增强 */
.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.service-card button:hover {
    background: #333333;
}

/* 认证页面样式 */
.auth-container {
    max-width: 500px;
    margin: 2rem auto;
    padding: 2rem;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: #000000;
    margin-bottom: 2rem;
}

.auth-header .auth-tabs {
    display: inline-flex;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 2rem;
}

.auth-card .auth-tabs {
    display: flex;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.tab-btn {
    padding: 0.8rem 1.5rem;
    border: none;
    background: white;
    cursor: pointer;
    transition: all 0.3s;
    border-right: 1px solid #e0e0e0;
    flex: 1;
}

.tab-btn:last-child {
    border-right: none;
}

.tab-btn.active {
    background: #000000;
    color: white;
}

.auth-content {
    display: flex;
    justify-content: center;
}

.auth-form {
    display: none;
    width: 100%;
}

.auth-form.active {
    display: block;
}

.auth-form h2 {
    text-align: left;
    margin-bottom: 1.5rem;
    color: #000000;
    font-size: 1.5rem;
}

.auth-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.auth-card h3 {
    margin-bottom: 1.5rem;
    color: #000000;
    font-size: 1.1rem;
}

.auth-switch {
    text-align: center;
    margin: 1.5rem 0;
    color: #666;
}

.switch-btn {
    background: none;
    border: none;
    color: #000000;
    text-decoration: underline;
    cursor: pointer;
    font-size: inherit;
}

.switch-btn:hover {
    color: #333;
}

.other-methods {
    margin-top: 2rem;
    text-align: center;
}

.other-methods p {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.other-method-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.other-method-btn {
    padding: 0.8rem 1.5rem;
    border: 1px solid #e0e0e0;
    background: #f5f5f5;
    color: #999;
    cursor: not-allowed;
    border-radius: 5px;
    font-size: 0.9rem;
    opacity: 0.6;
}

/* 个人中心样式 */
.profile-container {
    max-width: 800px;
    margin: 2rem auto;
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
}

.profile-form {
    margin-top: 1rem;
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.form-section:last-of-type {
    border-bottom: none;
}

.form-section h3 {
    color: #000000;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.form-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 2rem;
    gap: 1rem;
}

.form-actions .submit-btn {
    width: 100%;
    max-width: 300px;
}

.skip-link {
    color: #666;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s;
    text-align: center;
}

.skip-link:hover {
    color: #000000;
    text-decoration: underline;
}

/* 用户菜单样式 */
#user-menu {
    position: relative;
    cursor: pointer;
    display: inline-block;
}

#user-menu > span {
    color: #000000;
    text-decoration: none;
    padding: 0.5rem 1rem;
    display: inline-block;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: none;
    min-width: 150px;
    z-index: 1000;
    white-space: nowrap;
}

#user-menu:hover .dropdown {
    display: block;
}

.dropdown a {
    display: block;
    padding: 0.8rem 1rem;
    color: #333;
    text-decoration: none;
    border-bottom: 1px solid #f0f0f0;
    transition: background 0.3s;
    white-space: nowrap;
}

.dropdown a:last-child {
    border-bottom: none;
}

.dropdown a:hover {
    background: #f8f8f8;
}

@media (max-width: 768px) {
    .service-grid {
        grid-template-columns: 1fr;
    }

    .hero h1 {
        font-size: 2rem;
    }

    nav {
        padding: 1rem 2%;
    }

    .auth-container, .profile-container {
        margin: 1rem;
        padding: 1.5rem;
    }

    .auth-methods {
        flex-direction: column;
    }

    .auth-method-btn {
        min-width: auto;
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .form-actions .submit-btn {
        min-width: auto;
        order: 1;
    }

    .form-actions .skip-link {
        order: 2;
        text-align: center;
        padding: 0.5rem;
    }

    .auth-title {
        font-size: 2rem;
    }

    .auth-card {
        padding: 1.5rem;
    }

    .other-method-buttons {
        flex-direction: column;
        align-items: center;
    }

    .other-method-btn {
        width: 100%;
        max-width: 200px;
    }
}