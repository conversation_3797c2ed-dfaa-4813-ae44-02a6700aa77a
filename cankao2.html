<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TinyDest - 命理分析与占卜</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f7f7f7;
            color: #1a1a1a;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        .card {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            display: flex;
            flex-direction: column;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }
        .card-content {
            flex-grow: 1;
        }
        .card-button {
            margin-top: 1rem;
            background-color: #1a1a1a;
            color: #ffffff;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            align-self: flex-start;
            text-align: center;
        }
        .card-button:hover {
            background-color: #333333;
        }
        .btn {
            background-color: #1a1a1a;
            color: #ffffff;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .btn:hover:enabled {
            background-color: #333333;
        }
        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .input-group input, .input-group select {
            padding: 0.5rem;
            border: 1px solid #cccccc;
            border-radius: 4px;
            width: 100%;
        }
        .hidden {
            display: none;
        }
        .placeholder-text::placeholder {
            color: #999;
        }
    </style>
</head>
<body class="bg-white text-gray-900">

    <!-- 主页 -->
    <main id="home-page" class="container">
        <!-- 导航栏 -->
        <header class="flex justify-between items-center py-4 mb-8">
            <div class="flex items-center space-x-2">
                <h1 class="text-3xl font-bold">TinyDest</h1>
            </div>
            <nav>
                <button onclick="showPage('home-page')" class="mx-2 hover:underline">主页</button>
                <button id="auth-nav-btn" onclick="showPage('auth-page')" class="mx-2 hover:underline">登录/注册</button>
                <button id="profile-nav-btn" onclick="showPage('user-profile-page')" class="mx-2 hover:underline hidden">个人中心</button>
                <button id="logout-nav-btn" onclick="handleLogout()" class="mx-2 hover:underline hidden">登出</button>
            </nav>
        </header>

        <!-- 横幅标题 -->
        <div class="text-center my-12">
            <h2 class="text-4xl font-bold mb-2">AI 智能命理分析系统</h2>
            <p class="text-lg text-gray-600">all the power on earth can't change destiny</p>
        </div>

        <section class="space-y-8">
            <!-- 八字分析 -->
            <div class="space-y-4">
                <h3 class="text-2xl font-bold">八字分析</h3>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="card space-y-2" onclick="showInputForm('命盘解析')">
                        <div class="card-content">
                            <h4 class="text-xl font-bold">命盘解析</h4>
                            <p class="text-sm text-gray-600">揭示个人命盘特质与发展规律</p>
                        </div>
                        <button class="card-button w-full">分析</button>
                    </div>
                    <div class="card space-y-2" onclick="showInputForm('每日运势')">
                        <div class="card-content">
                            <h4 class="text-xl font-bold">每日运势</h4>
                            <p class="text-sm text-gray-600">基于八字的每日运势分析，助你把握每日吉凶</p>
                        </div>
                        <button class="card-button w-full">分析</button>
                    </div>
                    <div class="card space-y-2" onclick="showInputForm('合婚分析')">
                        <div class="card-content">
                            <h4 class="text-xl font-bold">合婚分析</h4>
                            <p class="text-sm text-gray-600">基于八字的深度匹配分析，揭示双方关系契合度</p>
                        </div>
                        <button class="card-button w-full">分析</button>
                    </div>
                    <div class="card space-y-2" onclick="showInputForm('事业合作')">
                        <div class="card-content">
                            <h4 class="text-xl font-bold">事业合作</h4>
                            <p class="text-sm text-gray-600">基于八字的商业关系分析，助你了解合作潜力与挑战</p>
                        </div>
                        <button class="card-button w-full">分析</button>
                    </div>
                    <div class="card space-y-2" onclick="showInputForm('婆媳关系')">
                        <div class="card-content">
                            <h4 class="text-xl font-bold">婆媳关系</h4>
                            <p class="text-sm text-gray-600">婆媳关系分析，揭示潜在互动模式与改善关系的方法</p>
                        </div>
                        <button class="card-button w-full">分析</button>
                    </div>
                    <div class="card space-y-2" onclick="showInputForm('知己分析')">
                        <div class="card-content">
                            <h4 class="text-xl font-bold">知己分析</h4>
                            <p class="text-sm text-gray-600">基于八字的朋友关系分析，揭示契合度与友谊潜力</p>
                        </div>
                        <button class="card-button w-full">分析</button>
                    </div>
                    <div class="card space-y-2" onclick="showInputForm('领导下属')">
                        <div class="card-content">
                            <h4 class="text-xl font-bold">领导下属</h4>
                            <p class="text-sm text-gray-600">基于八字的领导下属关系分析，助你了解沟通与合作模式</p>
                        </div>
                        <button class="card-button w-full">分析</button>
                    </div>
                    <div class="card space-y-2" onclick="showInputForm('父子关系')">
                        <div class="card-content">
                            <h4 class="text-xl font-bold">父子关系</h4>
                            <p class="text-sm text-gray-600">基于八字的父子关系分析，揭示潜在互动模式与改善关系的方法</p>
                        </div>
                        <button class="card-button w-full">分析</button>
                    </div>
                    <div class="card space-y-2" onclick="showInputForm('母子关系')">
                        <div class="card-content">
                            <h4 class="text-xl font-bold">母子关系</h4>
                            <p class="text-sm text-gray-600">基于八字的母子关系分析，揭示潜在互动模式与改善关系的方法</p>
                        </div>
                        <button class="card-button w-full">分析</button>
                    </div>
                </div>
            </div>

            <!-- 梅花易数 -->
            <div class="space-y-4">
                <h3 class="text-2xl font-bold">梅花易数</h3>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="card space-y-2" onclick="showInputForm('每日决策')">
                        <div class="card-content">
                            <h4 class="text-xl font-bold">每日决策</h4>
                            <p class="text-sm text-gray-600">基于梅花易数的每日决策指导，助你做出重要选择</p>
                        </div>
                        <button class="card-button w-full">分析</button>
                    </div>
                    <div class="card space-y-2" onclick="showInputForm('事业决策')">
                        <div class="card-content">
                            <h4 class="text-xl font-bold">事业决策</h4>
                            <p class="text-sm text-gray-600">基于梅花易数的事业决策指导，洞察职场发展机遇</p>
                        </div>
                        <button class="card-button w-full">分析</button>
                    </div>
                    <div class="card space-y-2" onclick="showInputForm('投资理财')">
                        <div class="card-content">
                            <h4 class="text-xl font-bold">投资理财</h4>
                            <p class="text-sm text-gray-600">基于梅花易数的投资理财指导，助你做出明智的财务选择</p>
                        </div>
                        <button class="card-button w-full">分析</button>
                    </div>
                </div>
            </div>

            <!-- 黄历查询 -->
            <div class="space-y-4">
                <h3 class="text-2xl font-bold">黄历查询</h3>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="card space-y-2" onclick="showInputForm('黄历查询')">
                        <div class="card-content">
                            <h4 class="text-xl font-bold">黄历查询</h4>
                            <p class="text-sm text-gray-600">传统中国黄历，查询各类活动的吉日</p>
                        </div>
                        <button class="card-button w-full">分析</button>
                    </div>
                    <div class="card space-y-2" onclick="showInputForm('择日分析')">
                        <div class="card-content">
                            <h4 class="text-xl font-bold">择日分析</h4>
                            <p class="text-sm text-gray-600">基于黄历的择日分析，助你选择最佳行动时机</p>
                        </div>
                        <button class="card-button w-full">分析</button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 登录/注册页面 -->
    <div id="auth-page" class="hidden container">
        <header class="flex justify-between items-center py-4 mb-8">
            <h1 class="text-3xl font-bold">TinyDest</h1>
            <nav>
                <button onclick="showPage('home-page')" class="mx-2 hover:underline">主页</button>
                <button id="auth-nav-btn-2" onclick="showPage('auth-page')" class="mx-2 hover:underline">登录/注册</button>
                <button id="profile-nav-btn-2" onclick="showPage('user-profile-page')" class="mx-2 hover:underline hidden">个人中心</button>
                <button id="logout-nav-btn-2" onclick="handleLogout()" class="mx-2 hover:underline hidden">登出</button>
            </nav>
        </header>
        <div class="flex flex-col items-center mt-12">
            <h2 class="text-3xl font-bold mb-6">登录或注册</h2>
            <div class="w-full max-w-sm card p-8">
                <p id="auth-info" class="text-center text-sm mb-4">用户名/密码登录</p>
                <form id="auth-form" class="space-y-4">
                    <div id="username-login" class="space-y-4">
                        <div class="input-group">
                            <label for="username" class="block mb-1 text-sm font-medium">用户名 *</label>
                            <input type="text" id="username" name="username" required>
                        </div>
                        <div class="input-group">
                            <label for="password" class="block mb-1 text-sm font-medium">密码 *</label>
                            <input type="password" id="password" name="password" required>
                        </div>
                        <button type="submit" class="btn w-full">登录</button>
                        <div class="mt-4 text-center text-sm">
                            <p>还没有账户？<button type="button" onclick="toggleAuthMode()" class="text-blue-600 hover:underline">点击注册</button></p>
                        </div>
                    </div>
                    <div id="username-register" class="space-y-4 hidden">
                        <div class="input-group">
                            <label for="reg-username" class="block mb-1 text-sm font-medium">用户名 *</label>
                            <input type="text" id="reg-username" name="reg-username" required>
                        </div>
                        <div class="input-group">
                            <label for="reg-password" class="block mb-1 text-sm font-medium">密码 *</label>
                            <input type="password" id="reg-password" name="reg-password" required>
                        </div>
                        <button type="submit" class="btn w-full">注册</button>
                        <div class="mt-4 text-center text-sm">
                            <p>已有账户？<button type="button" onclick="toggleAuthMode()" class="text-blue-600 hover:underline">点击登录</button></p>
                        </div>
                    </div>
                </form>
                <div class="mt-4 text-center text-sm">
                    <p>或选择其他登录方式:</p>
                </div>
                <div class="space-y-4 mt-2">
                    <button class="btn w-full" onclick="handleLogin('phone')">手机验证码登录</button>
                    <button class="btn w-full" onclick="handleLogin('google')">Google 登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 个人信息输入页面 -->
    <div id="input-form-page" class="hidden container">
        <header class="flex justify-between items-center py-4 mb-8">
            <h1 class="text-3xl font-bold">TinyDest</h1>
            <nav>
                <button onclick="showPage('home-page')" class="mx-2 hover:underline hidden">返回主页</button>
                <button id="auth-nav-btn-3" onclick="showPage('auth-page')" class="mx-2 hover:underline">登录/注册</button>
                <button id="profile-nav-btn-3" onclick="showPage('user-profile-page')" class="mx-2 hover:underline hidden">个人中心</button>
                <button id="logout-nav-btn-3" onclick="handleLogout()" class="mx-2 hover:underline hidden">登出</button>
            </nav>
        </header>
        <div class="flex flex-col items-center mt-12">
            <h2 id="input-form-title" class="text-3xl font-bold mb-6"></h2>
            <div class="w-full max-w-sm card p-8">
                <p class="text-sm text-gray-600 text-center mb-4">
                    <button onclick="showPage('home-page')" class="underline hover:no-underline">返回主页</button>
                </p>
                <form id="personal-info-form" class="space-y-4">
                    <!-- 表单字段将由 JavaScript 动态生成 -->
                </form>
            </div>
        </div>
    </div>
    
    <!-- 个人中心页面 -->
    <div id="user-profile-page" class="hidden container">
        <header class="flex justify-between items-center py-4 mb-8">
            <h1 class="text-3xl font-bold">TinyDest</h1>
            <nav>
                <button onclick="showPage('home-page')" class="mx-2 hover:underline">主页</button>
                <button id="auth-nav-btn-4" onclick="showPage('auth-page')" class="mx-2 hover:underline">登录/注册</button>
                <button id="profile-nav-btn-4" onclick="showPage('user-profile-page')" class="mx-2 hover:underline hidden">个人中心</button>
                <button id="logout-nav-btn-4" onclick="handleLogout()" class="mx-2 hover:underline hidden">登出</button>
            </nav>
        </header>
        <div class="flex flex-col items-center mt-12">
            <h2 class="text-3xl font-bold mb-6">个人中心</h2>
            <div class="w-full max-w-sm card p-8">
                <h3 class="text-xl font-bold mb-4">我的信息</h3>
                <form id="user-profile-form" class="space-y-4">
                    <div class="input-group">
                        <label for="profile-name" class="block mb-1 text-sm font-medium">姓名</label>
                        <input type="text" id="profile-name" name="name">
                    </div>
                    <div class="input-group">
                        <label for="profile-birthdate" class="block mb-1 text-sm font-medium">出生日期</label>
                        <input type="date" id="profile-birthdate" name="birthdate">
                    </div>
                    <div class="input-group">
                        <label for="profile-birthtime" class="block mb-1 text-sm font-medium">出生时间</label>
                        <input type="time" id="profile-birthtime" name="birthtime">
                    </div>
                    <div class="input-group">
                        <label for="profile-location" class="block mb-1 text-sm font-medium">出生地点</label>
                        <input type="text" id="profile-location" name="location">
                    </div>
                    <div class="input-group">
                        <label for="profile-gender" class="block mb-1 text-sm font-medium">性别</label>
                        <select id="profile-gender" name="gender">
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                    <div class="flex items-center justify-between">
                        <button type="submit" class="btn">保存修改</button>
                        <button type="button" onclick="showPage('home-page')" class="text-sm text-gray-600 hover:underline">稍后填写</button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    <script>
        const moduleTypes = {
            '命盘解析': 'single-person',
            '每日运势': 'single-person',
            '每日决策': 'single-person',
            '事业决策': 'single-person',
            '投资理财': 'single-person',
            '合婚分析': 'two-person',
            '事业合作': 'two-person',
            '婆媳关系': 'two-person',
            '知己分析': 'two-person',
            '领导下属': 'two-person',
            '父子关系': 'two-person',
            '母子关系': 'two-person',
            '黄历查询': 'single-person',
            '择日分析': 'single-person',
        };
        
        const modulePersonLabels = {
            '合婚分析': '配偶',
            '事业合作': '合作伙伴',
            '婆媳关系': '对方',
            '知己分析': '朋友',
            '领导下属': '对方',
            '父子关系': '对方',
            '母子关系': '对方',
            '黄历查询': '他人',
            '择日分析': '他人',
        };

        const moduleRelationships = {
            '婆媳关系': ['婆婆', '媳妇'],
            '父子关系': ['父亲', '儿子'],
            '母子关系': ['母亲', '儿子'],
            '领导下属': ['领导', '下属'],
        };

        let loggedInUserData = null; // 存储用户数据

        // 更新导航栏状态
        function updateNav() {
            const loggedIn = isLoggedIn();
            document.querySelectorAll('[id^="auth-nav-btn"]').forEach(btn => btn.classList.toggle('hidden', loggedIn));
            document.querySelectorAll('[id^="profile-nav-btn"]').forEach(btn => btn.classList.toggle('hidden', !loggedIn));
            document.querySelectorAll('[id^="logout-nav-btn"]').forEach(btn => btn.classList.toggle('hidden', !loggedIn));
        }

        // 检查用户是否登录
        function isLoggedIn() {
            return loggedInUserData !== null;
        }

        // 切换登录和注册表单
        function toggleAuthMode() {
            const loginForm = document.getElementById('username-login');
            const registerForm = document.getElementById('username-register');
            const authInfo = document.getElementById('auth-info');
            loginForm.classList.toggle('hidden');
            registerForm.classList.toggle('hidden');
            if (loginForm.classList.contains('hidden')) {
                authInfo.textContent = '用户名/密码登录';
            } else {
                authInfo.textContent = '用户名/密码注册';
            }
        }

        // 动态创建输入字段的函数
        function createInputField(id, label, required = true, type = 'text', options = []) {
            const group = document.createElement('div');
            group.className = 'input-group';
            
            const labelEl = document.createElement('label');
            labelEl.textContent = `${label}${required ? ' *' : ''}`;
            labelEl.className = 'block mb-1 text-sm font-medium';
            labelEl.htmlFor = id;
            group.appendChild(labelEl);
            
            let inputEl;
            if (type === 'select') {
                inputEl = document.createElement('select');
                for (const option of options) {
                    const optionEl = document.createElement('option');
                    optionEl.value = option;
                    optionEl.textContent = option;
                    inputEl.appendChild(optionEl);
                }
            } else if (type === 'textarea') {
                inputEl = document.createElement('textarea');
                inputEl.rows = 4;
            }
            else {
                inputEl = document.createElement('input');
                inputEl.type = type;
                if (type === 'date') {
                    inputEl.addEventListener('change', (e) => {
                        if (e.target.value) {
                            const year = new Date(e.target.value).getFullYear();
                            if (String(year).length !== 4) {
                                e.target.setCustomValidity('请输入4位年份');
                            } else {
                                e.target.setCustomValidity('');
                            }
                        }
                    });
                }
            }
            if (type === 'textarea') {
                inputEl.placeholder = '请描述你想咨询的具体问题';
                inputEl.classList.add('placeholder-text');
            }

            inputEl.id = id;
            inputEl.name = id;
            inputEl.required = required;
            inputEl.className = 'w-full p-2 border border-gray-300 rounded-md';
            group.appendChild(inputEl);
            
            return group;
        }

        // 生成表单的函数
        function generateForm(moduleName) {
            const formContainer = document.getElementById('personal-info-form');
            formContainer.innerHTML = '';
            
            const moduleType = moduleTypes[moduleName];

            // 自己
            const selfSection = document.createElement('div');
            selfSection.className = 'space-y-4 mb-8';
            const selfTitle = document.createElement('h4');
            selfTitle.className = 'text-lg font-semibold';
            selfTitle.textContent = '自己';
            selfSection.appendChild(selfTitle);
            
            const selfFields = {
                name: { label: '姓名', required: true },
                birthdate: { label: '出生日期', required: true, type: 'date' },
                birthtime: { label: '出生时间', required: false, type: 'time' },
                location: { label: '出生地点', required: true },
                gender: { label: '性别', required: true, type: 'select', options: ['男', '女'] },
            };
            
            for (const key in selfFields) {
                const field = selfFields[key];
                selfSection.appendChild(createInputField(`self-${key}`, field.label, field.required, field.type, field.options));
            }

            formContainer.appendChild(selfSection);

            // 梅花易数模块添加问题输入框
            if (['每日决策', '事业决策', '投资理财'].includes(moduleName)) {
                formContainer.appendChild(createInputField('question', '具体问题', true, 'textarea'));
            }

            // 黄历查询和择日分析添加活动类型下拉框
            if (['黄历查询', '择日分析'].includes(moduleName)) {
                formContainer.appendChild(createInputField('activity-type', '活动类型', true, 'select', ['结婚', '开业', '搬家', '出行', '投资', '其他']));
            }

            // 如果是双人分析，添加“对方”
            if (moduleType === 'two-person') {
                const otherSection = document.createElement('div');
                otherSection.className = 'space-y-4';
                const otherTitle = document.createElement('h4');
                otherTitle.className = 'text-lg font-semibold';
                otherTitle.textContent = modulePersonLabels[moduleName] || '对方';
                otherSection.appendChild(otherTitle);

                const otherFields = {
                    relationship: { label: '关系', required: true, type: 'select', options: moduleRelationships[moduleName] || [] },
                    name: { label: '姓名', required: true },
                    birthdate: { label: '出生日期', required: !['合婚分析', '事业合作'].includes(moduleName), type: 'date' },
                    birthtime: { label: '出生时间', required: false, type: 'time' },
                    location: { label: '出生地点', required: false },
                    gender: { label: '性别', required: true, type: 'select', options: ['男', '女'] },
                };

                for (const key in otherFields) {
                    const field = otherFields[key];
                    if (key === 'relationship' && !moduleRelationships[moduleName]) continue;
                    otherSection.appendChild(createInputField(`other-${key}`, field.label, field.required, field.type, field.options));
                }

                formContainer.appendChild(otherSection);
            }
            
            const submitBtn = document.createElement('button');
            submitBtn.type = 'submit';
            submitBtn.className = 'btn w-full mt-8';
            submitBtn.textContent = '分析';
            submitBtn.disabled = true;
            formContainer.appendChild(submitBtn);

            // 添加事件监听器来检查表单状态
            formContainer.addEventListener('input', () => {
                const form = document.getElementById('personal-info-form');
                const requiredInputs = form.querySelectorAll('[required]');
                let allFilled = true;
                requiredInputs.forEach(input => {
                    if (!input.value) {
                        allFilled = false;
                    }
                });
                submitBtn.disabled = !allFilled;
            });

            // 如果已登录，预填充数据
            if (isLoggedIn()) {
                const selfData = {
                    name: loggedInUserData.name,
                    birthdate: loggedInUserData.birthdate,
                    birthtime: loggedInUserData.birthtime,
                    location: loggedInUserData.location,
                    gender: loggedInUserData.gender
                };

                for (const key in selfData) {
                    const el = document.getElementById(`self-${key}`);
                    if (el) el.value = selfData[key];
                }
                
                // 触发一次输入事件以更新按钮状态
                formContainer.dispatchEvent(new Event('input'));
            }
        }

        // 页面跳转函数
        function showPage(pageId) {
            const pages = ['home-page', 'auth-page', 'input-form-page', 'user-profile-page'];
            pages.forEach(id => {
                document.getElementById(id).classList.add('hidden');
            });
            document.getElementById(pageId).classList.remove('hidden');
            updateNav();
        }

        // 显示输入表单
        function showInputForm(title) {
            document.getElementById('input-form-title').textContent = title;
            generateForm(title);
            showPage('input-form-page');
        }

        // 模拟登录
        function handleLogin() {
            // 模拟登录成功，实际应用中会从后端获取数据
            loggedInUserData = {
                username: 'testuser',
                name: '张三',
                birthdate: '1990-01-01',
                birthtime: '12:00',
                location: '北京',
                gender: '男'
            };
            alert('登录成功！');
            showPage('user-profile-page'); // 登录后直接进入个人中心
        }

        // 模拟登出
        function handleLogout() {
            loggedInUserData = null;
            alert('您已登出。');
            showPage('home-page');
        }

        // 处理个人中心表单提交
        document.getElementById('user-profile-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            loggedInUserData = { ...loggedInUserData, ...data };
            alert('个人信息已保存！');
        });
        
        // 监听个人中心表单输入，同步到内存中
        document.getElementById('user-profile-form').addEventListener('input', (e) => {
            const field = e.target.name;
            const value = e.target.value;
            if (loggedInUserData) {
                loggedInUserData[field] = value;
            }
        });

        // 初始页面加载时，填充个人中心表单
        document.addEventListener('DOMContentLoaded', () => {
            if (isLoggedIn()) {
                const profileForm = document.getElementById('user-profile-page');
                const profileData = {
                    name: loggedInUserData.name,
                    birthdate: loggedInUserData.birthdate,
                    birthtime: loggedInUserData.birthtime,
                    location: loggedInUserData.location,
                    gender: loggedInUserData.gender
                };
                for (const key in profileData) {
                    const el = profileForm.querySelector(`[name="${key}"]`);
                    if (el) el.value = profileData[key];
                }
            }
            updateNav();
        });
    </script>
</body>
</html>
