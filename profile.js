// 个人中心页面的JavaScript逻辑

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    checkLoginStatus()
    
    // 加载用户信息
    loadUserProfile()
    
    // 个人信息表单提交
    document.getElementById('profileForm').addEventListener('submit', async function(e) {
        e.preventDefault()
        
        const formData = new FormData(this)
        const data = {
            name: formData.get('name'),
            birthdate: formData.get('birthdate'),
            birthtime: formData.get('birthtime'),
            gender: formData.get('gender'),
            location: formData.get('location'),
            phone: formData.get('phone'),
            email: formData.get('email'),
            occupation: formData.get('occupation'),
            notes: formData.get('notes')
        }
        
        try {
            const response = await fetch('/api/profile', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            
            const result = await response.json()
            
            if (result.success) {
                alert('信息保存成功！')
            } else {
                alert(result.message || '保存失败')
            }
        } catch (error) {
            console.error('保存个人信息错误:', error)
            alert('保存失败，请重试')
        }
    })
})

// 检查登录状态
async function checkLoginStatus() {
    try {
        const response = await fetch('/api/user')
        const result = await response.json()
        
        if (!result.success || !result.user) {
            // 未登录，跳转到登录页面
            alert('请先登录')
            window.location.href = 'login.html'
            return
        }
        
        // 显示用户名
        const usernameDisplay = document.getElementById('username-display')
        if (usernameDisplay) {
            usernameDisplay.textContent = result.user.username
        }
    } catch (error) {
        console.error('检查登录状态失败:', error)
        window.location.href = 'login.html'
    }
}

// 加载用户个人信息
async function loadUserProfile() {
    try {
        const response = await fetch('/api/profile')
        const result = await response.json()
        
        if (result.success && result.profile) {
            const profile = result.profile
            
            // 填充表单数据
            const form = document.getElementById('profileForm')
            Object.keys(profile).forEach(key => {
                const input = form.querySelector(`[name="${key}"]`)
                if (input && profile[key]) {
                    input.value = profile[key]
                }
            })
        }
    } catch (error) {
        console.error('加载个人信息失败:', error)
    }
}

// 退出登录
async function logout() {
    try {
        const response = await fetch('/api/logout', {
            method: 'POST'
        })
        
        const result = await response.json()
        
        if (result.success) {
            alert('已退出登录')
            window.location.href = 'index.html'
        }
    } catch (error) {
        console.error('退出登录失败:', error)
        alert('退出登录失败')
    }
}
