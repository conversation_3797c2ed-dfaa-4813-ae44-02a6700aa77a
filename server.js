const express = require("express")
const path = require("path")
require("dotenv").config() // 加载 .env 文件
const OpenAI = require("openai") // 导入 OpenAI 库
const Database = require("better-sqlite3")
const bcrypt = require("bcryptjs")
const session = require("express-session")

const app = express()
const PORT = process.env.PORT || 3000

// 初始化数据库
const db = new Database("tinydest.db")

// 创建用户表
db.exec(`
  CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )
`)

// 创建用户资料表
db.exec(`
  CREATE TABLE IF NOT EXISTS user_profiles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    name TEXT,
    birthdate TEXT,
    birthtime TEXT,
    gender TEXT,
    location TEXT,
    phone TEXT,
    email TEXT,
    occupation TEXT,
    notes TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
  )
`)

// 初始化 OpenAI 客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_BASE_URL,
})

// 中间件
app.use(express.json())

// 会话中间件
app.use(
  session({
    secret: process.env.SESSION_SECRET || "tinydest-secret-key",
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: false, // 在生产环境中应该设置为 true (需要 HTTPS)
      maxAge: 24 * 60 * 60 * 1000, // 24小时
    },
  })
)

// 设置静态文件服务，明确指定MIME类型
app.use(
  express.static(".", {
    setHeaders: (res, path) => {
      if (path.endsWith(".js")) {
        res.setHeader("Content-Type", "application/javascript; charset=utf-8")
      } else if (path.endsWith(".css")) {
        res.setHeader("Content-Type", "text/css; charset=utf-8")
      } else if (path.endsWith(".html")) {
        res.setHeader("Content-Type", "text/html; charset=utf-8")
      }
    },
  })
)

// 用户注册API
app.post("/api/register", async (req, res) => {
  try {
    const { username, password } = req.body

    if (!username || !password) {
      return res.json({
        success: false,
        message: "用户名和密码不能为空",
      })
    }

    if (password.length < 6) {
      return res.json({
        success: false,
        message: "密码长度至少6位",
      })
    }

    // 检查用户名是否已存在
    const existingUser = db
      .prepare("SELECT id FROM users WHERE username = ?")
      .get(username)
    if (existingUser) {
      return res.json({
        success: false,
        message: "用户名已存在",
      })
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10)

    // 创建用户
    const result = db
      .prepare("INSERT INTO users (username, password) VALUES (?, ?)")
      .run(username, hashedPassword)

    // 创建空的用户资料
    db.prepare("INSERT INTO user_profiles (user_id) VALUES (?)").run(
      result.lastInsertRowid
    )

    res.json({
      success: true,
      message: "注册成功",
    })
  } catch (error) {
    console.error("注册错误:", error)
    res.json({
      success: false,
      message: "注册失败，请重试",
    })
  }
})

// 用户登录API
app.post("/api/login", async (req, res) => {
  try {
    const { username, password } = req.body

    if (!username || !password) {
      return res.json({
        success: false,
        message: "用户名和密码不能为空",
      })
    }

    // 查找用户
    const user = db
      .prepare("SELECT id, username, password FROM users WHERE username = ?")
      .get(username)
    if (!user) {
      return res.json({
        success: false,
        message: "用户名或密码错误",
      })
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      return res.json({
        success: false,
        message: "用户名或密码错误",
      })
    }

    // 设置会话
    req.session.userId = user.id
    req.session.username = user.username

    res.json({
      success: true,
      message: "登录成功",
      redirect: "profile.html",
    })
  } catch (error) {
    console.error("登录错误:", error)
    res.json({
      success: false,
      message: "登录失败，请重试",
    })
  }
})

// 用户退出API
app.post("/api/logout", (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      console.error("退出登录错误:", err)
      return res.json({
        success: false,
        message: "退出登录失败",
      })
    }
    res.json({
      success: true,
      message: "已退出登录",
    })
  })
})

// 获取当前用户信息API
app.get("/api/user", (req, res) => {
  if (!req.session.userId) {
    return res.json({
      success: false,
      message: "未登录",
    })
  }

  res.json({
    success: true,
    user: {
      id: req.session.userId,
      username: req.session.username,
    },
  })
})

// 获取用户资料API
app.get("/api/profile", (req, res) => {
  if (!req.session.userId) {
    return res.json({
      success: false,
      message: "未登录",
    })
  }

  try {
    const profile = db
      .prepare(
        "SELECT name, birthdate, birthtime, gender, location, phone, email, occupation, notes FROM user_profiles WHERE user_id = ?"
      )
      .get(req.session.userId)

    res.json({
      success: true,
      profile: profile || {},
    })
  } catch (error) {
    console.error("获取用户资料错误:", error)
    res.json({
      success: false,
      message: "获取用户资料失败",
    })
  }
})

// 更新用户资料API
app.put("/api/profile", (req, res) => {
  if (!req.session.userId) {
    return res.json({
      success: false,
      message: "未登录",
    })
  }

  try {
    const {
      name,
      birthdate,
      birthtime,
      gender,
      location,
      phone,
      email,
      occupation,
      notes,
    } = req.body

    // 更新用户资料
    const result = db
      .prepare(
        `UPDATE user_profiles
         SET name = ?, birthdate = ?, birthtime = ?, gender = ?, location = ?,
             phone = ?, email = ?, occupation = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
         WHERE user_id = ?`
      )
      .run(
        name,
        birthdate,
        birthtime,
        gender,
        location,
        phone,
        email,
        occupation,
        notes,
        req.session.userId
      )

    if (result.changes === 0) {
      // 如果没有更新任何行，可能是用户资料不存在，创建一个新的
      db.prepare(
        `INSERT INTO user_profiles
         (user_id, name, birthdate, birthtime, gender, location, phone, email, occupation, notes)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
      ).run(
        req.session.userId,
        name,
        birthdate,
        birthtime,
        gender,
        location,
        phone,
        email,
        occupation,
        notes
      )
    }

    res.json({
      success: true,
      message: "资料保存成功",
    })
  } catch (error) {
    console.error("更新用户资料错误:", error)
    res.json({
      success: false,
      message: "保存资料失败，请重试",
    })
  }
})

// API路由 - 命理分析 (流式响应)
app.post("/api/analyze", async (req, res) => {
  try {
    const { name, birthdate, birthtime, gender, location, analysisType } =
      req.body

    // 设置流式响应头 - 强制禁用缓存和代理缓存
    res.setHeader("Content-Type", "text/plain; charset=utf-8")
    res.setHeader(
      "Cache-Control",
      "no-cache, no-store, must-revalidate, private"
    )
    res.setHeader("Pragma", "no-cache")
    res.setHeader("Expires", "0")
    res.setHeader("Connection", "keep-alive")
    res.setHeader("X-Accel-Buffering", "no") // 禁用 Nginx 缓存
    res.setHeader("Transfer-Encoding", "chunked") // 明确指定分块传输

    // Cloudflare 专用头部 - 禁用压缩和缓存
    res.setHeader("CF-Cache-Status", "BYPASS")
    res.setHeader("Content-Encoding", "identity") // 禁用压缩
    res.setHeader("X-Content-Type-Options", "nosniff")
    res.setHeader("Vary", "Accept-Encoding") // 告诉 Cloudflare 不要基于编码缓存

    // 构建分析提示词
    const prompt = buildAnalysisPrompt(req.body)

    // 调用流式LLM API
    await callLLMAPIStream(prompt, res)
  } catch (error) {
    console.error("分析错误:", error)
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        error: "分析失败，请稍后重试",
      })
    } else {
      res.write('\n\n<div class="error">分析过程中出现错误，请重新尝试</div>')
      res.end()
    }
  }
})

// API路由 - SSE流式响应 (服务器环境备用方案)
app.post("/api/analyze-sse", async (req, res) => {
  try {
    const { name, birthdate, birthtime, gender, location, analysisType } =
      req.body

    // 设置SSE响应头
    res.setHeader("Content-Type", "text/event-stream")
    res.setHeader(
      "Cache-Control",
      "no-cache, no-store, must-revalidate, private"
    )
    res.setHeader("Connection", "keep-alive")
    res.setHeader("Access-Control-Allow-Origin", "*")
    res.setHeader("Access-Control-Allow-Headers", "Cache-Control")
    res.setHeader("X-Accel-Buffering", "no")

    // Cloudflare SSE 专用头部
    res.setHeader("CF-Cache-Status", "BYPASS")
    res.setHeader("Content-Encoding", "identity")
    res.setHeader("X-Content-Type-Options", "nosniff")

    // 发送初始连接确认
    res.write('data: {"type":"connected"}\n\n')
    res.flush && res.flush()

    // 构建分析提示词
    const prompt = buildAnalysisPrompt(req.body)

    // 调用SSE流式LLM API
    await callLLMAPIStreamSSE(prompt, res)
  } catch (error) {
    console.error("SSE分析错误:", error)
    res.write(`data: {"type":"error","message":"${error.message}"}\n\n`)
    res.end()
  }
})

// 测试流式响应接口
app.get("/api/test-stream", (req, res) => {
  // 设置强化的流式响应头
  res.setHeader("Content-Type", "text/plain; charset=utf-8")
  res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate, private")
  res.setHeader("Pragma", "no-cache")
  res.setHeader("Expires", "0")
  res.setHeader("Connection", "keep-alive")
  res.setHeader("X-Accel-Buffering", "no")
  res.setHeader("Transfer-Encoding", "chunked")

  // Cloudflare 专用头部
  res.setHeader("CF-Cache-Status", "BYPASS")
  res.setHeader("Content-Encoding", "identity")
  res.setHeader("X-Content-Type-Options", "nosniff")
  res.setHeader("Vary", "Accept-Encoding")

  console.log("开始测试流式响应...")

  let count = 0
  const interval = setInterval(() => {
    count++
    const message = `数据块 ${count}: ${new Date().toISOString()}\n`
    res.write(message)

    if (res.flush) {
      res.flush()
    }

    console.log(`发送数据块 ${count}`)

    if (count >= 10) {
      clearInterval(interval)
      res.write("流式响应测试完成!\n")
      res.end()
      console.log("流式响应测试结束")
    }
  }, 500) // 每500ms发送一个数据块
})

// 构建分析提示词
function buildAnalysisPrompt(data) {
  const {
    name,
    birthdate,
    birthtime,
    gender,
    location,
    analysisType,
    partner_relationship,
    partner_name,
    partner_birthdate,
    partner_birthtime,
    partner_gender,
    partner_location,
    question,
    date_query,
    event_type,
  } = data

  const typePrompts = {
    mingpan:
      "请根据提供的出生信息进行全面的命盘解析，包括性格特点、运势走向、事业发展、感情状况等方面",
    daily: "请分析今日的运势情况，包括财运、事业运、感情运、健康运等",
    marriage:
      "请分析两人的婚姻配对情况，包括性格匹配度、感情发展趋势、婚姻建议等",
    career: "请分析两人的事业合作运势，包括合作潜力、互补性、注意事项等",
    family: "请分析婆媳关系，包括性格互动模式、相处建议、化解矛盾的方法等",
    friendship:
      "请分析两人的友谊关系，包括性格契合度、友谊发展潜力、相处建议等",
    leadership:
      "请分析领导与下属的关系模式，包括管理风格匹配度、沟通建议、团队协作等",
    father_son: "请分析父子关系，包括性格互动模式、沟通方式、关系改善建议等",
    mother_son: "请分析母子关系，包括性格互动模式、沟通方式、关系改善建议等",
    decision: "请基于梅花易数为今日决策提供指导，结合个人八字分析最佳行动方向",
    business: "请运用梅花易数分析事业发展方向和重要商业决策",
    investment: "请运用梅花易数指导投资理财决策，分析时机和方向",
    calendar: "请根据传统黄历和个人八字，分析指定日期的吉凶宜忌",
    date_selection: "请根据个人八字选择最适合的吉日良辰",
  }

  // 构建基础信息
  let promptText = `
作为一位专业的命理分析师，请根据以下信息进行分析：

【本人信息】
姓名：${name}
出生日期：${birthdate}
出生时间：${birthtime}
性别：${gender === "male" ? "男" : "女"}
出生地点：${location}`

  // 根据分析类型添加额外信息
  if (
    partner_name &&
    [
      "marriage",
      "career",
      "family",
      "friendship",
      "leadership",
      "father_son",
      "mother_son",
    ].includes(analysisType)
  ) {
    promptText += `

【对方信息】`
    if (partner_relationship) {
      const relationshipMap = {
        spouse: "配偶",
        partner: "伴侣",
        colleague: "同事",
        boss: "上司",
        subordinate: "下属",
        friend: "朋友",
        father: "父亲",
        mother: "母亲",
        son: "儿子",
        daughter: "女儿",
        other: "其他",
      }
      promptText += `
关系：${relationshipMap[partner_relationship] || partner_relationship}`
    }
    promptText += `
姓名：${partner_name}
出生日期：${partner_birthdate}
出生时间：${partner_birthtime}
性别：${partner_gender === "male" ? "男" : "女"}`
    if (partner_location) {
      promptText += `
出生地点：${partner_location}`
    }
  }

  if (
    question &&
    ["decision", "business", "investment"].includes(analysisType)
  ) {
    promptText += `

【咨询问题】
${question}`
  }

  if (date_query && ["calendar", "date_selection"].includes(analysisType)) {
    promptText += `

【查询日期】
${date_query}`

    if (event_type) {
      const eventTypes = {
        wedding: "结婚",
        business: "开业",
        moving: "搬家",
        travel: "出行",
        investment: "投资",
        other: "其他",
      }
      promptText += `
【活动类型】
${eventTypes[event_type] || event_type}`
    }
  }

  promptText += `

【分析要求】
${typePrompts[analysisType] || typePrompts.mingpan}

请提供详细、专业且富有洞察力的分析，内容要积极正面，给出实用的建议。

重要格式要求：
- 请直接返回HTML格式的内容，不要使用markdown
- 使用HTML标签如 <h3>、<p>、<strong>、<ul>、<li> 等来格式化内容
- 标题使用 <h3 class="section-title">标题</h3>
- 重要内容使用 <strong>标签
- 列表使用 <ul><li>项目</li></ul>
- 不要包含 <html>、<body> 等完整页面标签，只返回内容部分
- 用中文回复，语言要通俗易懂，富有温度

示例格式：
<div class="fortune-analysis">
<h3 class="section-title">🌟 基本性格特征</h3>
<p>您的性格分析内容...</p>
<h3 class="section-title">✨ 运势分析</h3>
<p>运势内容...</p>
</div>
    `

  return promptText
}

// 调用流式LLM API (使用 OpenAI 库和 .env 配置)
async function callLLMAPIStream(prompt, res) {
  try {
    console.log("正在调用流式 LLM API...")

    const stream = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "deepseek-chat",
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.7,
      max_tokens: 2000,
      stream: true, // 启用流式响应
    })

    let fullContent = ""
    let chunkCount = 0

    // 发送初始数据以建立连接
    res.write(" ")
    res.flush && res.flush()

    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || ""
      if (content) {
        fullContent += content
        chunkCount++

        // 实时发送内容到客户端
        res.write(content)

        // 强制刷新缓冲区，确保数据立即发送
        if (res.flush) {
          res.flush()
        }

        // 每隔几个chunk添加一个空格，防止代理服务器缓存
        if (chunkCount % 10 === 0) {
          res.write("")
          if (res.flush) {
            res.flush()
          }
        }

        // 添加小延迟，让客户端有时间处理数据
        await new Promise((resolve) => setTimeout(resolve, 10))
      }
    }

    console.log("流式 LLM API 调用成功，总共发送了", chunkCount, "个数据块")
    res.end() // 结束响应
  } catch (error) {
    console.error("流式 LLM API 调用失败:", error.message)
    throw error
  }
}

// 调用SSE流式LLM API (服务器环境优化版本)
async function callLLMAPIStreamSSE(prompt, res) {
  try {
    console.log("正在调用SSE流式 LLM API...")

    const stream = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "deepseek-chat",
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.7,
      max_tokens: 2000,
      stream: true,
    })

    let fullContent = ""
    let chunkCount = 0

    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || ""
      if (content) {
        fullContent += content
        chunkCount++

        // 发送SSE格式的数据
        const sseData = {
          type: "content",
          content: content,
          chunk: chunkCount,
        }

        res.write(`data: ${JSON.stringify(sseData)}\n\n`)

        // 强制刷新
        if (res.flush) {
          res.flush()
        }

        // 每隔5个chunk发送心跳
        if (chunkCount % 5 === 0) {
          res.write(`data: {"type":"heartbeat","chunk":${chunkCount}}\n\n`)
          if (res.flush) {
            res.flush()
          }
        }
      }
    }

    // 发送完成信号
    res.write(`data: {"type":"complete","totalChunks":${chunkCount}}\n\n`)
    console.log("SSE流式 LLM API 调用成功，总共发送了", chunkCount, "个数据块")
    res.end()
  } catch (error) {
    console.error("SSE流式 LLM API 调用失败:", error.message)
    throw error
  }
}

// 调用LLM API (非流式，备用)
async function callLLMAPI(prompt) {
  try {
    console.log("正在调用 LLM API...")

    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "deepseek-chat",
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.7,
      max_tokens: 2000,
    })

    const result = completion.choices[0].message.content
    console.log("LLM API 调用成功")
    return result
  } catch (error) {
    console.error("LLM API 调用失败:", error.message)

    // 如果 API 调用失败，返回友好的错误信息
    throw new Error("AI 分析服务暂时不可用，请稍后重试")
  }
}

// 启动服务器
app.listen(PORT, () => {
  console.log(`算命网站服务器运行在 http://localhost:${PORT}`)
})
