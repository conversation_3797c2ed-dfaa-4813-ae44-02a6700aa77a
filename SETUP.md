# TinyDest 项目设置指南

## 环境要求
- Node.js (推荐 v16 或更高版本)
- npm 或 yarn

## 安装步骤

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
创建 `.env` 文件并添加以下配置：
```
# OpenAI API 配置
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=https://api.deepseek.com/v1
OPENAI_MODEL=deepseek-chat

# 会话密钥（生产环境请使用随机字符串）
SESSION_SECRET=your_session_secret_here
```

### 3. 启动服务器
```bash
# 开发模式（自动重启）
npm run dev

# 生产模式
npm start
```

### 4. 访问网站
打开浏览器访问：`http://localhost:3000`

## 功能说明

### 账户系统
1. **注册/登录**：访问 `/login.html`
   - 目前只支持用户名/密码方式
   - 手机验证码和Google登录为预留功能（显示为灰色）

2. **个人中心**：访问 `/profile.html`
   - 需要先登录
   - 可以填写个人信息（所有字段都是选填）
   - 支持"保存修改"和"稍后填写"

### 命理分析
1. **八字分析**：包含9个模块
   - 命盘解析、每日运势、合婚分析、事业合作
   - 婆媳关系、知己分析、领导下属、父子关系、母子关系

2. **梅花易数**：包含3个模块
   - 每日决策、事业决策、投资理财

3. **黄历查询**：包含2个模块
   - 黄历查询、择日分析

### 数据预填充
- 用户登录并保存个人信息后
- 再次进入分析页面时会自动预填充个人信息
- 用户可以随时修改预填充的信息

## 数据库
项目使用 SQLite 数据库（`tinydest.db`），包含：
- `users` 表：用户账户信息
- `user_profiles` 表：用户详细资料

数据库会在首次启动时自动创建。

## 注意事项
1. 确保 `.env` 文件中的 API 密钥有效
2. 生产环境请修改 `SESSION_SECRET` 为随机字符串
3. 数据库文件会自动创建在项目根目录
