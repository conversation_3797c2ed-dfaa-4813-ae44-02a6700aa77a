<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TinyDest - AI智能命理分析系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">TinyDest</div>
            <div class="nav-links">
                <a href="index.html">主页</a>
                <a href="login.html" id="auth-link">登录/注册</a>
            </div>
        </nav>
    </header>

    <main>
        <section class="hero">
            <h1>AI智能命理分析系统</h1>
            <p>all the power on earth can't change destiny</p>
        </section>

        <!-- 八字分析 -->
        <section class="services">
            <h2>八字分析</h2>
            <div class="service-grid">
                <div class="service-card">
                    <h3>命盘解析</h3>
                    <p>揭示个人命盘特质与发展规律</p>
                    <button onclick="goToAnalysis('mingpan')">分析</button>
                </div>
                <div class="service-card">
                    <h3>每日运势</h3>
                    <p>基于八字的每日运势分析，助你把握每日吉凶</p>
                    <button onclick="goToAnalysis('daily')">分析</button>
                </div>
                <div class="service-card">
                    <h3>合婚分析</h3>
                    <p>基于八字的深度匹配分析，揭示双方关系契合度</p>
                    <button onclick="goToAnalysis('marriage')">分析</button>
                </div>
                <div class="service-card">
                    <h3>事业合作</h3>
                    <p>基于八字的商业关系分析，助你了解合作潜力与挑战</p>
                    <button onclick="goToAnalysis('career')">分析</button>
                </div>
                <div class="service-card">
                    <h3>婆媳关系</h3>
                    <p>婆媳关系分析，揭示潜在互动模式与改善关系的方法</p>
                    <button onclick="goToAnalysis('family')">分析</button>
                </div>
                <div class="service-card">
                    <h3>知己分析</h3>
                    <p>基于八字的朋友关系分析，揭示契合度与友谊潜力</p>
                    <button onclick="goToAnalysis('friendship')">分析</button>
                </div>
                <div class="service-card">
                    <h3>领导下属</h3>
                    <p>基于八字的领导下属关系分析，助你了解沟通与合作模式</p>
                    <button onclick="goToAnalysis('leadership')">分析</button>
                </div>
                <div class="service-card">
                    <h3>父子关系</h3>
                    <p>基于八字的父子关系分析，揭示潜在互动模式与改善关系的方法</p>
                    <button onclick="goToAnalysis('father_son')">分析</button>
                </div>
                <div class="service-card">
                    <h3>母子关系</h3>
                    <p>基于八字的母子关系分析，揭示潜在互动模式与改善关系的方法</p>
                    <button onclick="goToAnalysis('mother_son')">分析</button>
                </div>
            </div>
        </section>

        <!-- 梅花易数 -->
        <section class="services">
            <h2>梅花易数</h2>
            <div class="service-grid">
                <div class="service-card">
                    <h3>每日决策</h3>
                    <p>基于梅花易数的每日决策指导，助你做出重要选择</p>
                    <button onclick="goToAnalysis('decision')">分析</button>
                </div>
                <div class="service-card">
                    <h3>事业决策</h3>
                    <p>基于梅花易数的事业决策指导，洞察职场发展机遇</p>
                    <button onclick="goToAnalysis('business')">分析</button>
                </div>
                <div class="service-card">
                    <h3>投资理财</h3>
                    <p>基于梅花易数的投资理财指导，助你做出明智的财务选择</p>
                    <button onclick="goToAnalysis('investment')">分析</button>
                </div>
            </div>
        </section>

        <!-- 黄历查询 -->
        <section class="services">
            <h2>黄历查询</h2>
            <div class="service-grid">
                <div class="service-card">
                    <h3>黄历查询</h3>
                    <p>传统中国黄历，查询各类活动的吉日</p>
                    <button onclick="goToAnalysis('calendar')">分析</button>
                </div>
                <div class="service-card">
                    <h3>择日分析</h3>
                    <p>基于黄历的择日分析，助你选择最佳行动时机</p>
                    <button onclick="goToAnalysis('date_selection')">分析</button>
                </div>
            </div>
        </section>
    </main>

    <script>
        function goToAnalysis(type) {
            console.log('点击分析类型:', type);
            // 通过URL参数传递分析类型
            window.location.href = `analysis.html?type=${type}`;
        }

        // 检查登录状态并更新导航
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                const response = await fetch('/api/user');
                const result = await response.json();

                if (result.success && result.user) {
                    // 已登录，更新导航链接
                    const authLink = document.getElementById('auth-link');
                    authLink.innerHTML = `
                        <span>${result.user.username}</span>
                        <div class="dropdown">
                            <a href="profile.html">个人中心</a>
                            <a href="#" onclick="logout()">退出登录</a>
                        </div>
                    `;
                    authLink.href = '#';
                    authLink.id = 'user-menu';
                }
            } catch (error) {
                console.log('未登录或检查登录状态失败');
            }
        });

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/api/logout', {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    alert('已退出登录');
                    location.reload();
                }
            } catch (error) {
                console.error('退出登录失败:', error);
                alert('退出登录失败');
            }
        }
    </script>
</body>
</html>
