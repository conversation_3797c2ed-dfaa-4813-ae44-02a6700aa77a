# Nginx 配置示例 - 用于支持流式响应
# 将此配置添加到您的 Nginx 服务器配置中

server {
    listen 80;
    server_name your-domain.com;

    # 禁用代理缓存，确保流式响应正常工作
    proxy_buffering off;
    proxy_cache off;
    proxy_request_buffering off;

    # 设置超时时间
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 300s;

    # 静态文件服务
    location / {
        try_files $uri $uri/ @nodejs;
    }

    # Node.js 应用代理
    location @nodejs {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 关键：禁用缓存以支持流式响应
        proxy_buffering off;
        proxy_cache off;
        proxy_request_buffering off;
    }

    # 专门为流式API配置
    location /api/analyze {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 流式响应关键配置
        proxy_buffering off;
        proxy_cache off;
        proxy_request_buffering off;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        
        # 添加响应头
        add_header X-Accel-Buffering no;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # SSE 流式响应配置
    location /api/analyze-sse {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # SSE 专用配置
        proxy_buffering off;
        proxy_cache off;
        proxy_request_buffering off;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        
        # SSE 响应头
        add_header X-Accel-Buffering no;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Headers "Cache-Control";
    }
}

# Apache 配置示例 (.htaccess 或虚拟主机配置)
# 如果使用 Apache 而不是 Nginx，请使用以下配置：

# <VirtualHost *:80>
#     ServerName your-domain.com
#     DocumentRoot /path/to/your/app
#     
#     # 禁用输出缓存
#     SetEnv no-gzip 1
#     SetEnv dont-vary 1
#     
#     # 代理到 Node.js 应用
#     ProxyPreserveHost On
#     ProxyRequests Off
#     
#     # 流式响应配置
#     ProxyPass /api/ http://localhost:3000/api/
#     ProxyPassReverse /api/ http://localhost:3000/api/
#     
#     # 禁用代理缓存
#     ProxyPassReverse /api/ http://localhost:3000/api/ nocanon
#     
#     # 设置响应头
#     Header always set X-Accel-Buffering "no"
#     Header always set Cache-Control "no-cache, no-store, must-revalidate"
#     Header always set Pragma "no-cache"
#     Header always set Expires "0"
# </VirtualHost>

# PM2 配置示例 (ecosystem.config.js)
# module.exports = {
#   apps: [{
#     name: 'mingli-app',
#     script: 'server.js',
#     instances: 1,
#     autorestart: true,
#     watch: false,
#     max_memory_restart: '1G',
#     env: {
#       NODE_ENV: 'production',
#       PORT: 3000
#     }
#   }]
# }

# Docker 配置示例
# 如果使用 Docker 部署，确保在 docker-compose.yml 中设置：
# services:
#   app:
#     build: .
#     ports:
#       - "3000:3000"
#     environment:
#       - NODE_ENV=production
#     # 禁用缓存
#     tmpfs:
#       - /tmp
#     volumes:
#       - .:/app
#       - /app/node_modules
