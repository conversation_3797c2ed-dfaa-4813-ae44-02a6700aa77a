<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式响应测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .result {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            min-height: 100px;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .stream-content::after {
            content: '▋';
            animation: blink 1s infinite;
            color: #667eea;
        }
        .stream-content.complete::after {
            display: none;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <h1>流式响应测试页面</h1>
    
    <div class="test-container">
        <h3>测试流式API</h3>
        <button onclick="testStream()">开始测试</button>
        <div id="result" class="result"></div>
    </div>

    <script>
        async function testStream() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="stream-content">正在连接...</div>';
            
            const testData = {
                name: "测试用户",
                birthdate: "1990-01-01",
                birthtime: "12:00",
                gender: "male",
                location: "北京",
                analysisType: "mingpan"
            };

            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const streamContainer = resultDiv.querySelector('.stream-content');
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                let buffer = '';
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        break;
                    }
                    
                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;
                    
                    // 直接显示HTML内容
                    streamContainer.innerHTML = buffer;
                }
                
                streamContainer.classList.add('complete');
                console.log('流式响应测试完成');
                
            } catch (error) {
                console.error('测试失败:', error);
                resultDiv.innerHTML = `<div class="error">测试失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
