# 流式响应部署指南

## 问题描述

在本地开发环境中，流式响应工作正常，但部署到服务器后，界面会卡很久然后一次性显示所有结果。这是因为服务器环境中的代理服务器（如 Nginx、Apache）或 CDN 会缓存响应内容。

## 解决方案

### 1. 服务器端优化

我们已经在代码中实现了以下优化：

#### 强化的响应头设置
```javascript
res.setHeader("Content-Type", "text/plain; charset=utf-8")
res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate")
res.setHeader("Pragma", "no-cache")
res.setHeader("Expires", "0")
res.setHeader("Connection", "keep-alive")
res.setHeader("X-Accel-Buffering", "no") // 禁用 Nginx 缓存
res.setHeader("Transfer-Encoding", "chunked") // 明确指定分块传输
```

#### 双重流式响应方案
- **主要方案**: `/api/analyze` - 普通流式响应
- **备用方案**: `/api/analyze-sse` - Server-Sent Events 格式

#### 强制刷新缓冲区
```javascript
res.write(content)
if (res.flush) {
  res.flush() // 强制刷新缓冲区
}
```

### 2. Nginx 配置

如果使用 Nginx 作为反向代理，请添加以下配置：

```nginx
location /api/analyze {
    proxy_pass http://localhost:3000;
    proxy_http_version 1.1;
    
    # 关键：禁用所有缓存
    proxy_buffering off;
    proxy_cache off;
    proxy_request_buffering off;
    
    # 设置超时
    proxy_read_timeout 300s;
    proxy_send_timeout 300s;
    
    # 添加响应头
    add_header X-Accel-Buffering no;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
}
```

完整配置请参考 `nginx.conf.example` 文件。

### 3. Apache 配置

如果使用 Apache，请添加：

```apache
# 禁用输出缓存
SetEnv no-gzip 1
SetEnv dont-vary 1

# 设置响应头
Header always set X-Accel-Buffering "no"
Header always set Cache-Control "no-cache, no-store, must-revalidate"
```

### 4. CDN 配置

如果使用 CDN（如 Cloudflare），请：

1. 在 CDN 控制面板中禁用对 `/api/analyze*` 路径的缓存
2. 设置 Page Rules 绕过缓存：
   - URL: `your-domain.com/api/analyze*`
   - 设置: Cache Level = Bypass

### 5. 前端自动切换

前端代码已实现自动切换机制：

```javascript
try {
  // 首先尝试普通流式响应
  await handleStreamResponse(response)
} catch (streamError) {
  // 如果失败，自动切换到 SSE 方式
  await handleSSEResponse(data)
}
```

## 测试流式响应

### 1. 本地测试
```bash
# 启动服务器
npm start

# 访问测试页面
http://localhost:3000/test-stream.html
```

### 2. 服务器测试

使用 curl 测试流式响应：

```bash
# 测试普通流式响应
curl -X POST http://your-domain.com/api/analyze \
  -H "Content-Type: application/json" \
  -d '{"name":"测试","birthdate":"1990-01-01","birthtime":"12:00","gender":"male","location":"北京","analysisType":"mingpan"}' \
  --no-buffer

# 测试 SSE 响应
curl -X POST http://your-domain.com/api/analyze-sse \
  -H "Content-Type: application/json" \
  -d '{"name":"测试","birthdate":"1990-01-01","birthtime":"12:00","gender":"male","location":"北京","analysisType":"mingpan"}' \
  --no-buffer
```

如果看到数据逐步输出而不是一次性显示，说明流式响应工作正常。

## 常见问题排查

### 1. 检查响应头
在浏览器开发者工具的 Network 标签中检查响应头是否包含：
- `X-Accel-Buffering: no`
- `Cache-Control: no-cache, no-store, must-revalidate`
- `Transfer-Encoding: chunked`

### 2. 检查代理配置
确认 Nginx/Apache 配置中已禁用缓存：
- `proxy_buffering off`
- `proxy_cache off`

### 3. 检查 CDN 设置
确认 CDN 没有缓存 API 响应。

### 4. 查看服务器日志
检查 Node.js 应用日志，确认流式响应正在发送数据块。

## 部署建议

1. **使用 PM2 管理进程**：确保应用稳定运行
2. **配置 Nginx**：正确设置反向代理和缓存策略
3. **监控日志**：观察流式响应的工作状态
4. **测试验证**：部署后使用测试页面验证功能

## 性能优化

1. **调整 chunk 大小**：根据网络条件调整数据块大小
2. **设置合理超时**：避免长时间等待
3. **错误重试机制**：自动切换到备用方案
4. **监控指标**：跟踪响应时间和成功率

通过以上配置，应该能够解决服务器环境中流式响应被缓存的问题。
