<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的信息 - TinyDest</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">TinyDest</div>
            <div class="nav-links">
                <a href="index.html">主页</a>
                <a href="#" id="user-menu">
                    <span id="username-display">用户</span>
                    <div class="dropdown">
                        <a href="profile.html">个人中心</a>
                        <a href="#" onclick="logout()">退出登录</a>
                    </div>
                </a>
            </div>
        </nav>
    </header>

    <main>
        <section class="profile-container">
            <a href="index.html" class="back-link">返回主页</a>
            <h1>我的信息</h1>
            
            <form id="profileForm" class="profile-form">
                <div class="form-section">
                    <h3>基本信息</h3>
                    
                    <div class="form-group">
                        <label for="profile-name">姓名</label>
                        <input type="text" id="profile-name" name="name" placeholder="请输入您的姓名">
                    </div>

                    <div class="form-group">
                        <label for="profile-birthdate">出生日期</label>
                        <input type="date" id="profile-birthdate" name="birthdate">
                    </div>

                    <div class="form-group">
                        <label for="profile-birthtime">出生时间</label>
                        <input type="time" id="profile-birthtime" name="birthtime">
                    </div>

                    <div class="form-group">
                        <label for="profile-gender">性别</label>
                        <select id="profile-gender" name="gender">
                            <option value="">请选择</option>
                            <option value="male">男</option>
                            <option value="female">女</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="profile-location">出生地点</label>
                        <input type="text" id="profile-location" name="location" placeholder="例如：北京">
                    </div>
                </div>

                <div class="form-section">
                    <h3>联系信息</h3>
                    
                    <div class="form-group">
                        <label for="profile-phone">手机号</label>
                        <input type="tel" id="profile-phone" name="phone" placeholder="请输入手机号">
                    </div>

                    <div class="form-group">
                        <label for="profile-email">邮箱</label>
                        <input type="email" id="profile-email" name="email" placeholder="请输入邮箱地址">
                    </div>
                </div>

                <div class="form-section">
                    <h3>其他信息</h3>
                    
                    <div class="form-group">
                        <label for="profile-occupation">职业</label>
                        <input type="text" id="profile-occupation" name="occupation" placeholder="请输入您的职业">
                    </div>

                    <div class="form-group">
                        <label for="profile-notes">备注</label>
                        <textarea id="profile-notes" name="notes" rows="3" placeholder="其他需要记录的信息"></textarea>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="submit-btn">保存修改</button>
                    <a href="index.html" class="skip-link">稍后填写</a>
                </div>
            </form>
        </section>
    </main>

    <script src="profile.js"></script>
</body>
</html>
