# Cloudflare 流式响应配置指南

## 问题分析

从您的请求头可以看到：
- `server: cloudflare` - 使用了 Cloudflare CDN
- `content-encoding: zstd` - Cloudflare 启用了压缩
- `cf-cache-status: DYNAMIC` - 内容被标记为动态，但仍可能被缓存

Cloudflare 默认会：
1. **压缩响应内容** - 导致流式数据被缓冲
2. **缓存 API 响应** - 即使是 POST 请求也可能被缓存
3. **启用 Brotli/Gzip 压缩** - 阻止实时流式传输

## 解决方案

### 1. Cloudflare 控制面板配置

#### 步骤 1：创建 Page Rules
在 Cloudflare 控制面板中：

1. 进入 **Rules** → **Page Rules**
2. 创建新规则：
   - **URL**: `niubi.941989.xyz/api/analyze*`
   - **设置**:
     - Cache Level: **Bypass**
     - Browser Cache TTL: **Respect Existing Headers**
     - Security Level: **Medium**
     - Disable Apps: **On**
     - Disable Performance: **On**

#### 步骤 2：配置 Transform Rules
1. 进入 **Rules** → **Transform Rules** → **HTTP Response Header Modification**
2. 创建新规则：
   - **Rule name**: `Disable Stream Caching`
   - **When incoming requests match**: 
     - Field: `URI Path`
     - Operator: `starts with`
     - Value: `/api/analyze`
   - **Then**:
     - **Set static** `Cache-Control` = `no-cache, no-store, must-revalidate, private`
     - **Set static** `Content-Encoding` = `identity`
     - **Set static** `CF-Cache-Status` = `BYPASS`

#### 步骤 3：禁用压缩
1. 进入 **Speed** → **Optimization**
2. 对于 `/api/analyze*` 路径：
   - **Auto Minify**: 关闭 JavaScript, CSS, HTML
   - **Brotli**: 关闭
   - **Gzip**: 关闭

### 2. 使用 Cloudflare Workers (推荐)

创建一个 Worker 来处理流式响应：

```javascript
// cloudflare-worker.js
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url)
  
  // 只处理 API 分析请求
  if (url.pathname.startsWith('/api/analyze')) {
    // 直接转发到源服务器，禁用所有 Cloudflare 优化
    const response = await fetch(request, {
      cf: {
        // 禁用所有 Cloudflare 功能
        cacheEverything: false,
        cacheTtl: 0,
        polish: 'off',
        minify: {
          javascript: false,
          css: false,
          html: false
        },
        mirage: false,
        scrapeShield: false,
        apps: false
      }
    })
    
    // 修改响应头确保流式传输
    const newResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...response.headers,
        'Cache-Control': 'no-cache, no-store, must-revalidate, private',
        'Content-Encoding': 'identity',
        'CF-Cache-Status': 'BYPASS',
        'X-Accel-Buffering': 'no'
      }
    })
    
    return newResponse
  }
  
  // 其他请求正常处理
  return fetch(request)
}
```

### 3. 服务器端优化 (已实现)

代码中已添加 Cloudflare 专用响应头：

```javascript
// Cloudflare 专用头部 - 禁用压缩和缓存
res.setHeader("CF-Cache-Status", "BYPASS")
res.setHeader("Content-Encoding", "identity") // 禁用压缩
res.setHeader("X-Content-Type-Options", "nosniff")
res.setHeader("Vary", "Accept-Encoding") // 告诉 Cloudflare 不要基于编码缓存
```

### 4. 测试验证

#### 检查响应头
确认以下响应头存在：
```
Cache-Control: no-cache, no-store, must-revalidate, private
Content-Encoding: identity
CF-Cache-Status: BYPASS
X-Accel-Buffering: no
```

#### 使用 curl 测试
```bash
curl -X POST https://niubi.941989.xyz/api/analyze \
  -H "Content-Type: application/json" \
  -d '{"name":"测试","birthdate":"1990-01-01","birthtime":"12:00","gender":"male","location":"北京","analysisType":"mingpan"}' \
  -v --no-buffer
```

观察是否有逐步输出。

### 5. 备用方案：子域名绕过

如果上述方法仍不生效，可以使用子域名绕过 Cloudflare：

1. 创建子域名 `api.niubi.941989.xyz`
2. 将其设置为 **DNS Only** (灰色云朵)
3. 直接指向服务器 IP
4. 修改前端请求 URL：

```javascript
// 修改 analysis.js 中的 API 地址
const API_BASE = 'https://api.niubi.941989.xyz'

const response = await fetch(`${API_BASE}/api/analyze`, {
  // ... 其他配置
})
```

### 6. 紧急解决方案：降级到非流式

如果流式响应仍有问题，可以临时使用非流式响应：

```javascript
// 在 analysis.js 中添加配置
const USE_STREAMING = false // 设置为 false 禁用流式响应

if (USE_STREAMING) {
  await handleStreamResponse(response)
} else {
  // 使用传统的一次性响应
  const result = await response.json()
  displayResult(result.analysis)
}
```

## 推荐配置顺序

1. **首先尝试**: Page Rules + Transform Rules
2. **如果不行**: 部署 Cloudflare Worker
3. **最后方案**: 使用子域名绕过 Cloudflare
4. **紧急方案**: 临时禁用流式响应

## 注意事项

- Cloudflare 的缓存策略可能需要几分钟才能生效
- 清除浏览器缓存后再测试
- 可以使用 Cloudflare 的 **Purge Cache** 功能立即清除缓存
- 监控 Cloudflare Analytics 确认规则是否生效

通过以上配置，应该能够解决 Cloudflare 环境下的流式响应问题。
